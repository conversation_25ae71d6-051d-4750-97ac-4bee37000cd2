from Helpers.LLMRequest import create_llm_client, ModelType

def StoryPickerAgent(used_topics: list[str], topics: list[str], yahoo_news_titles: list[str] = None) -> list[str]:
    llm = create_llm_client(
        system_instruction=f"""# Youtube Video Content Selector

## Task Information
Your job is to create 3 ideas based on the provided trends and current financial news information.
You should select Finance related topics for the YouTube channel Liquid Finance.

## Current Financial News Context
You will be provided with current Yahoo Finance news titles to help you create relevant and timely content.
Use these news titles to identify trending financial topics, market movements, and current events that would be valuable for your audience.
Focus on topics that are currently happening and would provide immediate value to viewers.

## Don't repeat topics
You will be provided with a list of storys that have already been used today, we dont want to reuse topics.
Example if we already did a story on bitcoin, dont do another this applies to all topics.
We want to keep content unique and engaging.

## Examples of Good Topics
- Bitcoin Market Today
- YMAX Stock Today
- TSLA Stock Today
- Top Market Movers Today

## Rules
- Avoid things like "Best Stocks to Buy" or "Worst Stocks to Buy"
- Keep information impartive and to the point
- We provide information not financial advice
- Do not provide false information
- Do not provide misleading information
- Keep all topics related to finance
- Avoid politics
- Dont overhype stocks/crypto/commodities
- Up to 3 topic ideas per response
- Dont reuse topics if we did a story on bitcoin today, dont do another one (same with all other topics)
- Use the provided trending search terms and news headlines to create perfect topics

## Storys Used Today
{used_topics}

## Trending Search Terms
{topics}

## Output Format
You should follow this exact custom XML format:
<topics><topic>Topic 1</topic><topic>Topic 2</topic><topic>Topic 3</topic></topics>
""",
        enable_tools=False,
        model=ModelType.GEMINI_2_5_FLASH_LIGHT,
        enable_training_logging=True,
        agent_prefix="StoryPicker"
    )

    # Prepare Yahoo Finance news section
    yahoo_news_section = ""
    if yahoo_news_titles and len(yahoo_news_titles) > 0:
        yahoo_news_section = f"""## Current Yahoo Finance News Headlines:
{chr(10).join([f"- {title}" for title in yahoo_news_titles[:15]])}

"""

    response = llm.generate_response(f"""## Storys Used Today
{used_topics}

{yahoo_news_section}## Trending Topics:
{topics}
""")
    if response is None:
        return []

    # Parse response
    split_one = response.split("<topics>")[1]
    split_two = split_one.split("</topics>")[0]
    split_three = split_two.split("<topic>")[1:]
    topics = [split_three[i].split("</topic>")[0] for i in range(len(split_three))]

    return topics

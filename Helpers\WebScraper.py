"""
Web Scraper Client with Undetected Chrome Driver

A production-ready Python class for extracting text content from webpages
using undetected-chromedriver to bypass bot detection.

Requirements:
    pip install undetected-chromedriver selenium beautifulsoup4 lxml

Usage:
    from Helpers.WebScraper import WebScraperClient
    
    scraper = WebScraperClient()
    text_content = scraper.extract_text("https://example.com")
    scraper.close()
"""

import os
import time
import logging
import hashlib
from typing import Optional, Dict, Any, List, Union
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from threading import Lock
from pathlib import Path
import json

try:
    import undetected_chromedriver as uc
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.common.exceptions import (
        TimeoutException,
        WebDriverException,
        NoSuchElementException,
        StaleElementReferenceException
    )
except ImportError as e:
    raise ImportError(
        "Required dependencies not found. Please install: "
        "pip install undetected-chromedriver selenium"
    ) from e

try:
    from webdriver_manager.chrome import ChromeDriverManager
    WEBDRIVER_MANAGER_AVAILABLE = True
except ImportError:
    WEBDRIVER_MANAGER_AVAILABLE = False

try:
    from bs4 import BeautifulSoup
    BS4_AVAILABLE = True
except ImportError:
    BS4_AVAILABLE = False
    logging.warning("BeautifulSoup4 not available. HTML parsing will be limited.")

try:
    from lxml import html as lxml_html
    LXML_AVAILABLE = True
except ImportError:
    LXML_AVAILABLE = False
    logging.warning("lxml not available. Some parsing features will be limited.")


# Configure logging
logger = logging.getLogger(__name__)


@dataclass
class CacheEntry:
    """Cache entry with timestamp and data."""
    data: str
    timestamp: float
    expires_at: float
    url: str


@dataclass
class ScrapingResult:
    """Result object for web scraping operations."""
    url: str
    title: Optional[str]
    text_content: str
    meta_description: Optional[str]
    headings: List[str]
    links: List[Dict[str, str]]
    images: List[str]
    success: bool
    error_message: Optional[str] = None
    response_time: float = 0.0
    timestamp: datetime = field(default_factory=datetime.now)
    cached: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format."""
        return {
            "url": self.url,
            "title": self.title,
            "text_content": self.text_content,
            "meta_description": self.meta_description,
            "headings": self.headings,
            "links": self.links,
            "images": self.images,
            "success": self.success,
            "error_message": self.error_message,
            "response_time": self.response_time,
            "timestamp": self.timestamp.isoformat(),
            "cached": self.cached
        }


class WebScraperError(Exception):
    """Custom exception for web scraper errors."""
    pass


class WebScraperClient:
    """
    Production-ready web scraper using undetected-chromedriver.
    
    Features:
        - Undetected Chrome driver to bypass bot detection
        - Comprehensive text extraction with multiple parsers
        - Response caching with configurable TTL
        - Automatic retry mechanism with exponential backoff
        - Thread-safe operations
        - Resource cleanup and memory management
        - User agent rotation
        - Viewport randomization
        - JavaScript execution support
        - Multiple content extraction methods
    
    Example:
        >>> scraper = WebScraperClient(headless=True, cache_ttl=3600)
        >>> result = scraper.extract_text("https://example.com")
        >>> print(f"Title: {result.title}")
        >>> print(f"Content: {result.text_content[:100]}...")
        >>> scraper.close()
    """
    
    # Common user agents for rotation
    USER_AGENTS = [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    ]
    
    # Viewport sizes for randomization
    VIEWPORTS = [
        (1920, 1080),
        (1366, 768),
        (1536, 864),
        (1440, 900),
        (1280, 720)
    ]
    
    def __init__(
        self,
        headless: bool = True,
        timeout: int = 30,
        page_load_timeout: int = 30,
        cache_ttl: int = 3600,  # 1 hour
        enable_cache: bool = True,
        max_retries: int = 3,
        retry_delay: float = 1.0,
        user_data_dir: Optional[str] = None,
        proxy: Optional[str] = None,
        enable_javascript: bool = True,
        wait_for_content: bool = True,
        download_images: bool = False
    ):
        """
        Initialize the WebScraper client.
        
        Args:
            headless: Run browser in headless mode
            timeout: Element wait timeout in seconds
            page_load_timeout: Page load timeout in seconds
            cache_ttl: Cache time-to-live in seconds
            enable_cache: Enable response caching
            max_retries: Maximum retry attempts for failed requests
            retry_delay: Base delay between retries (exponential backoff)
            user_data_dir: Custom user data directory for Chrome
            proxy: Proxy server URL (format: "host:port")
            enable_javascript: Enable JavaScript execution
            wait_for_content: Wait for dynamic content to load
            download_images: Enable image downloading
        """
        self.headless = headless
        self.timeout = timeout
        self.page_load_timeout = page_load_timeout
        self.cache_ttl = cache_ttl
        self.enable_cache = enable_cache
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.user_data_dir = user_data_dir
        self.proxy = proxy
        self.enable_javascript = enable_javascript
        self.wait_for_content = wait_for_content
        self.download_images = download_images
        
        # Initialize cache and thread safety
        self._cache: Dict[str, CacheEntry] = {}
        self._cache_lock = Lock()
        
        # Browser management
        self._driver: Optional[uc.Chrome] = None
        self._driver_lock = Lock()
        self._is_closed = False
        
        # Performance tracking
        self._request_count = 0
        self._total_response_time = 0.0
        
        # Setup logging
        self.logger = logging.getLogger(__name__)
        
        self.logger.info(
            f"WebScraperClient initialized: headless={headless}, "
            f"cache_enabled={enable_cache}, timeout={timeout}s"
        )
    
    def _get_cache_key(self, url: str, **kwargs) -> str:
        """Generate cache key for URL and parameters."""
        key_data = {
            "url": url.lower().strip(),
            "headless": self.headless,
            "enable_javascript": self.enable_javascript,
            **kwargs
        }
        key_str = json.dumps(key_data, sort_keys=True)
        return hashlib.md5(key_str.encode()).hexdigest()
    
    def _get_from_cache(self, cache_key: str) -> Optional[ScrapingResult]:
        """Retrieve result from cache if valid."""
        if not self.enable_cache:
            return None
        
        with self._cache_lock:
            entry = self._cache.get(cache_key)
            if entry and time.time() < entry.expires_at:
                self.logger.debug(f"Cache hit for key: {cache_key}")
                # Reconstruct ScrapingResult from cached data
                result = ScrapingResult(
                    url=entry.url,
                    title=None,  # Basic cache stores only text content
                    text_content=entry.data,
                    meta_description=None,
                    headings=[],
                    links=[],
                    images=[],
                    success=True,
                    cached=True
                )
                return result
            elif entry:
                # Remove expired entry
                del self._cache[cache_key]
        
        return None
    
    def _save_to_cache(self, cache_key: str, url: str, text_content: str):
        """Save text content to cache."""
        if self.enable_cache:
            with self._cache_lock:
                expires_at = time.time() + self.cache_ttl
                self._cache[cache_key] = CacheEntry(
                    data=text_content,
                    timestamp=time.time(),
                    expires_at=expires_at,
                    url=url
                )
                self.logger.debug(f"Cached content for URL: {url}")
    
    def _setup_chrome_options(self) -> uc.ChromeOptions:
        """Configure Chrome options for optimal scraping."""
        options = uc.ChromeOptions()
        
        # Basic options
        if self.headless:
            options.add_argument('--headless=new')
        
        # Performance and stealth options
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--disable-web-security')
        options.add_argument('--disable-features=VizDisplayCompositor')
        options.add_argument('--disable-extensions')
        options.add_argument('--disable-plugins')
        options.add_argument('--disable-images') if not self.download_images else None
        
        # Memory optimization
        options.add_argument('--memory-pressure-off')
        options.add_argument('--max_old_space_size=4096')
        
        # Anti-detection options
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_argument('--disable-automation')
        options.add_experimental_option("prefs", {
            "profile.default_content_setting_values.notifications": 2,
            "profile.default_content_settings.popups": 0,
            "profile.managed_default_content_settings.images": 2 if not self.download_images else 1
        })
        
        # User data directory
        if self.user_data_dir:
            options.add_argument(f'--user-data-dir={self.user_data_dir}')
        
        # Proxy configuration
        if self.proxy:
            options.add_argument(f'--proxy-server={self.proxy}')
        
        # Random viewport
        import random
        width, height = random.choice(self.VIEWPORTS)
        options.add_argument(f'--window-size={width},{height}')
        
        return options
    
    def _init_driver(self) -> uc.Chrome:
        """Initialize the Chrome driver with optimal settings."""
        with self._driver_lock:
            if self._driver is None and not self._is_closed:
                try:
                    options = self._setup_chrome_options()
                    
                    self.logger.debug(f"Initializing Chrome with options: {[arg for arg in options.arguments]}")
                    
                    # Try to get correct ChromeDriver version using webdriver-manager
                    driver_path = None
                    if WEBDRIVER_MANAGER_AVAILABLE:
                        try:
                            driver_path = ChromeDriverManager().install()
                            self.logger.info(f"ChromeDriver managed at: {driver_path}")
                        except Exception as e:
                            self.logger.warning(f"ChromeDriver manager failed: {e}")

                    # Initialize undetected-chromedriver
                    self._driver = uc.Chrome(
                        options=options,
                        version_main=None,  # Auto-detect
                        driver_executable_path=driver_path,  # Use managed driver path
                    )
                    
                    # Configure timeouts
                    self._driver.set_page_load_timeout(self.page_load_timeout)
                    self._driver.implicitly_wait(self.timeout)
                    
                    # Anti-detection script
                    try:
                        self._driver.execute_script(
                            "Object.defineProperty(navigator, 'webdriver', {get: () => undefined})"
                        )
                    except Exception as script_error:
                        self.logger.warning(f"Failed to execute anti-detection script: {script_error}")
                    
                    # Random user agent
                    try:
                        import random
                        user_agent = random.choice(self.USER_AGENTS)
                        self._driver.execute_cdp_cmd('Network.setUserAgentOverride', {
                            "userAgent": user_agent
                        })
                    except Exception as ua_error:
                        self.logger.warning(f"Failed to set user agent: {ua_error}")
                    
                    self.logger.info("Chrome driver initialized successfully")
                    
                except Exception as e:
                    self.logger.error(f"Failed to initialize Chrome driver: {str(e)}")
                    # Log more detailed error information
                    if "invalid argument" in str(e).lower():
                        self.logger.error("Chrome options may be incompatible with current Chrome version")
                    if "excludeSwitches" in str(e):
                        self.logger.error("excludeSwitches option not supported - using alternative approach")
                    raise WebScraperError(f"Driver initialization failed: {e}")
        
        return self._driver
    
    def _extract_text_selenium(self, driver: uc.Chrome) -> str:
        """Extract text using Selenium's built-in methods."""
        try:
            # Wait for body element
            if self.wait_for_content:
                WebDriverWait(driver, self.timeout).until(
                    EC.presence_of_element_located((By.TAG_NAME, "body"))
                )
                # Additional wait for dynamic content
                time.sleep(2)
            
            # Get text from body
            body_element = driver.find_element(By.TAG_NAME, "body")
            return body_element.get_attribute("innerText") or ""
            
        except (TimeoutException, NoSuchElementException) as e:
            self.logger.warning(f"Selenium text extraction failed: {e}")
            return ""
    
    def _extract_text_beautifulsoup(self, html_content: str) -> str:
        """Extract text using BeautifulSoup for better parsing."""
        if not BS4_AVAILABLE:
            return ""
        
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Remove script and style elements
            for script in soup(["script", "style", "nav", "footer", "header"]):
                script.decompose()
            
            # Get text and clean it
            text = soup.get_text()
            
            # Clean up whitespace
            lines = (line.strip() for line in text.splitlines())
            chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
            text = ' '.join(chunk for chunk in chunks if chunk)
            
            return text
            
        except Exception as e:
            self.logger.warning(f"BeautifulSoup text extraction failed: {e}")
            return ""
    
    def _extract_comprehensive_data(self, driver: uc.Chrome) -> Dict[str, Any]:
        """Extract comprehensive data from the webpage."""
        data = {
            "title": None,
            "meta_description": None,
            "headings": [],
            "links": [],
            "images": []
        }
        
        try:
            # Extract title
            try:
                data["title"] = driver.title
            except Exception:
                pass
            
            # Extract meta description
            try:
                meta_desc = driver.find_element(
                    By.CSS_SELECTOR, 'meta[name="description"]'
                )
                data["meta_description"] = meta_desc.get_attribute("content")
            except NoSuchElementException:
                pass
            
            # Extract headings
            try:
                headings = driver.find_elements(By.CSS_SELECTOR, "h1, h2, h3, h4, h5, h6")
                data["headings"] = [h.text.strip() for h in headings if h.text.strip()]
            except Exception:
                pass
            
            # Extract links
            try:
                links = driver.find_elements(By.TAG_NAME, "a")
                data["links"] = [
                    {
                        "text": link.text.strip(),
                        "href": link.get_attribute("href")
                    }
                    for link in links
                    if link.get_attribute("href") and link.text.strip()
                ][:50]  # Limit to first 50 links
            except Exception:
                pass
            
            # Extract images
            if self.download_images:
                try:
                    images = driver.find_elements(By.TAG_NAME, "img")
                    data["images"] = [
                        img.get_attribute("src")
                        for img in images
                        if img.get_attribute("src")
                    ][:20]  # Limit to first 20 images
                except Exception:
                    pass
            
        except Exception as e:
            self.logger.warning(f"Comprehensive data extraction failed: {e}")
        
        return data
    
    def extract_text(
        self,
        url: str,
        force_refresh: bool = False,
        wait_time: Optional[float] = None,
        custom_selectors: Optional[List[str]] = None
    ) -> ScrapingResult:
        """
        Extract text content from a webpage.
        
        Args:
            url: URL to scrape
            force_refresh: Force fresh content (bypass cache)
            wait_time: Additional wait time after page load
            custom_selectors: Custom CSS selectors to extract specific content
            
        Returns:
            ScrapingResult object with extracted content
            
        Raises:
            WebScraperError: If scraping fails after retries
            ValueError: If URL is invalid
        """
        if not url or not url.strip():
            raise ValueError("URL cannot be empty")
        
        url = url.strip()
        start_time = time.time()
        
        # Validate URL format
        if not (url.startswith('http://') or url.startswith('https://')):
            url = 'https://' + url
        
        # Check cache first
        cache_key = self._get_cache_key(
            url, 
            custom_selectors=custom_selectors,
            wait_time=wait_time
        )
        
        if not force_refresh:
            cached_result = self._get_from_cache(cache_key)
            if cached_result:
                cached_result.response_time = time.time() - start_time
                return cached_result
        
        # Perform scraping with retries
        last_error = None
        
        for attempt in range(self.max_retries):
            try:
                self.logger.info(f"Scraping URL: {url} (attempt {attempt + 1})")
                
                # Initialize driver if needed
                driver = self._init_driver()
                
                # Navigate to URL
                driver.get(url)
                
                # Wait for additional time if specified
                if wait_time:
                    time.sleep(wait_time)
                
                # Extract comprehensive data
                comprehensive_data = self._extract_comprehensive_data(driver)
                
                # Extract text content using multiple methods
                text_content = ""
                
                # Method 1: Custom selectors
                if custom_selectors:
                    try:
                        for selector in custom_selectors:
                            elements = driver.find_elements(By.CSS_SELECTOR, selector)
                            selector_text = " ".join([elem.text for elem in elements])
                            text_content += selector_text + " "
                    except Exception as e:
                        self.logger.warning(f"Custom selector extraction failed: {e}")
                
                # Method 2: Selenium extraction
                if not text_content.strip():
                    text_content = self._extract_text_selenium(driver)
                
                # Method 3: BeautifulSoup extraction (fallback)
                if not text_content.strip() and BS4_AVAILABLE:
                    html_content = driver.page_source
                    text_content = self._extract_text_beautifulsoup(html_content)
                
                # Clean up text content
                text_content = text_content.strip()
                
                if not text_content:
                    raise WebScraperError("No text content extracted from webpage")
                
                # Create result
                result = ScrapingResult(
                    url=url,
                    title=comprehensive_data["title"],
                    text_content=text_content,
                    meta_description=comprehensive_data["meta_description"],
                    headings=comprehensive_data["headings"],
                    links=comprehensive_data["links"],
                    images=comprehensive_data["images"],
                    success=True,
                    response_time=time.time() - start_time
                )
                
                # Cache the result
                self._save_to_cache(cache_key, url, text_content)
                
                # Update performance tracking
                self._request_count += 1
                self._total_response_time += result.response_time
                
                self.logger.info(
                    f"Successfully extracted {len(text_content)} characters "
                    f"from {url} in {result.response_time:.2f}s"
                )
                
                return result
                
            except Exception as e:
                last_error = e
                self.logger.warning(
                    f"Scraping attempt {attempt + 1} failed for {url}: {e}"
                )
                
                if attempt < self.max_retries - 1:
                    # Exponential backoff
                    delay = self.retry_delay * (2 ** attempt)
                    self.logger.info(f"Retrying in {delay} seconds...")
                    time.sleep(delay)
                    
                    # Reset driver on error
                    self._close_driver()
        
        # All attempts failed
        error_msg = f"Failed to scrape {url} after {self.max_retries} attempts: {last_error}"
        self.logger.error(error_msg)
        
        return ScrapingResult(
            url=url,
            title=None,
            text_content="",
            meta_description=None,
            headings=[],
            links=[],
            images=[],
            success=False,
            error_message=str(last_error),
            response_time=time.time() - start_time
        )
    
    def batch_extract(
        self,
        urls: List[str],
        max_concurrent: int = 3,
        delay_between_requests: float = 1.0
    ) -> Dict[str, ScrapingResult]:
        """
        Extract text from multiple URLs in sequence.
        
        Args:
            urls: List of URLs to scrape
            max_concurrent: Maximum concurrent requests (currently sequential)
            delay_between_requests: Delay between requests in seconds
            
        Returns:
            Dictionary mapping URLs to their ScrapingResult objects
        """
        results = {}
        
        for i, url in enumerate(urls):
            try:
                self.logger.info(f"Processing URL {i+1}/{len(urls)}: {url}")
                result = self.extract_text(url)
                results[url] = result
                
                # Add delay between requests
                if i < len(urls) - 1:
                    time.sleep(delay_between_requests)
                    
            except Exception as e:
                self.logger.error(f"Failed to process URL {url}: {e}")
                results[url] = ScrapingResult(
                    url=url,
                    title=None,
                    text_content="",
                    meta_description=None,
                    headings=[],
                    links=[],
                    images=[],
                    success=False,
                    error_message=str(e)
                )
        
        return results
    
    def clear_cache(self):
        """Clear all cached responses."""
        with self._cache_lock:
            self._cache.clear()
        self.logger.info("Cache cleared")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        with self._cache_lock:
            current_time = time.time()
            valid_entries = sum(
                1 for entry in self._cache.values()
                if current_time < entry.expires_at
            )
            
            return {
                "total_entries": len(self._cache),
                "valid_entries": valid_entries,
                "expired_entries": len(self._cache) - valid_entries,
                "cache_hit_ratio": "N/A"  # Would need to track hits/misses
            }
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics."""
        avg_response_time = (
            self._total_response_time / self._request_count
            if self._request_count > 0 else 0
        )
        
        return {
            "total_requests": self._request_count,
            "total_response_time": self._total_response_time,
            "average_response_time": avg_response_time,
            "cache_enabled": self.enable_cache,
            "cache_stats": self.get_cache_stats()
        }
    
    def _close_driver(self):
        """Close the Chrome driver safely."""
        with self._driver_lock:
            if self._driver:
                try:
                    self._driver.quit()
                except Exception as e:
                    self.logger.warning(f"Error closing driver: {e}")
                finally:
                    self._driver = None
    
    def close(self):
        """Close the scraper and clean up resources."""
        self._is_closed = True
        self._close_driver()
        self.clear_cache()
        self.logger.info("WebScraperClient closed")
    
    def __enter__(self):
        """Context manager entry."""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.close()
    
    def __del__(self):
        """Destructor to ensure cleanup."""
        if not self._is_closed:
            self.close()


# Example usage and testing
if __name__ == "__main__":
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    try:
        # Initialize scraper
        with WebScraperClient(
            headless=True,
            timeout=15,
            enable_cache=True,
            cache_ttl=3600
        ) as scraper:
            
            # Example 1: Extract text from a single webpage
            print("=== Single Page Extraction ===")
            result = scraper.extract_text("https://example.com")
            
            if result.success:
                print(f"Title: {result.title}")
                print(f"Content Length: {len(result.text_content)} characters")
                print(f"Content Preview: {result.text_content[:200]}...")
                print(f"Response Time: {result.response_time:.2f}s")
                print(f"Headings: {result.headings[:3]}")  # First 3 headings
            else:
                print(f"Extraction failed: {result.error_message}")
            
            # Example 2: Batch extraction
            print("\n=== Batch Extraction ===")
            urls = [
                "https://example.com",
                "https://httpbin.org/html"
            ]
            
            batch_results = scraper.batch_extract(urls, delay_between_requests=2)
            
            for url, result in batch_results.items():
                print(f"URL: {url}")
                print(f"Success: {result.success}")
                print(f"Content Length: {len(result.text_content)}")
                print("---")
            
            # Example 3: Performance stats
            print("\n=== Performance Stats ===")
            stats = scraper.get_performance_stats()
            print(f"Total Requests: {stats['total_requests']}")
            print(f"Average Response Time: {stats['average_response_time']:.2f}s")
            print(f"Cache Stats: {stats['cache_stats']}")
            
    except WebScraperError as e:
        print(f"Scraper Error: {e}")
    except Exception as e:
        print(f"Unexpected error: {e}")
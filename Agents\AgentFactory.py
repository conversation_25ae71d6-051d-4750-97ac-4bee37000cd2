"""
Agent Factory for creating profile-aware video creation agents

This factory provides a centralized way to create and configure agents
with proper profile context and validation.
"""

from typing import Dict, Type, Any, Optional
import logging

from Helpers.ProfileManager import VideoProfile
from .BaseAgent import BaseAgent, AgentContext
from .StoryPicker import Story<PERSON><PERSON><PERSON>gent
from .SearchTerms import SearchTermsAgent
from .DataExtractor import DataExtractorAgent
from .ScriptWriter import ScriptWriterAgent
from .YouTubeMetadata import YouTubeMetadataAgent


class AgentFactoryError(Exception):
    """Custom exception for agent factory errors"""
    pass


class AgentFactory:
    """
    Factory for creating profile-aware video creation agents
    
    Features:
    - Centralized agent creation with proper context
    - Profile validation and configuration
    - Agent type registration and discovery
    - Consistent logging and error handling
    """
    
    def __init__(self):
        """Initialize the agent factory"""
        self.logger = logging.getLogger(__name__)
        
        # Registry of available agent types
        self._agent_registry: Dict[str, Type[BaseAgent]] = {
            'story_picker': StoryPickerAgent,
            'search_terms': SearchTermsAgent,
            'data_extractor': DataExtractorAgent,
            'script_writer': <PERSON>riptWriterAgent,
            'youtube_metadata': YouTubeMetadataAgent,
        }
    
    def create_agent(
        self, 
        agent_type: str, 
        profile: VideoProfile,
        session_data: Optional[Dict[str, Any]] = None
    ) -> BaseAgent:
        """
        Create an agent instance with proper profile context
        
        Args:
            agent_type: Type of agent to create (e.g., 'story_picker', 'script_writer')
            profile: Video profile configuration
            session_data: Optional session data for the agent context
            
        Returns:
            Configured agent instance
            
        Raises:
            AgentFactoryError: If agent creation fails
        """
        try:
            # Validate agent type
            if agent_type not in self._agent_registry:
                available_types = ', '.join(self._agent_registry.keys())
                raise AgentFactoryError(
                    f"Unknown agent type '{agent_type}'. Available types: {available_types}"
                )
            
            # Get agent class
            agent_class = self._agent_registry[agent_type]
            
            # Get agent-specific configuration
            agent_config = self._get_agent_config(agent_type, profile)
            
            # Create agent context
            context = AgentContext(
                profile=profile,
                agent_config=agent_config,
                session_data=session_data or {}
            )
            
            # Create and return agent instance
            agent = agent_class(context)
            
            self.logger.info(f"Created {agent_type} agent for profile '{profile.profile_info.name}'")
            return agent
            
        except Exception as e:
            error_msg = f"Failed to create {agent_type} agent: {str(e)}"
            self.logger.error(error_msg)
            raise AgentFactoryError(error_msg)
    
    def _get_agent_config(self, agent_type: str, profile: VideoProfile) -> Any:
        """
        Get agent-specific configuration from profile
        
        Args:
            agent_type: Type of agent
            profile: Video profile
            
        Returns:
            Agent configuration object
        """
        # Map agent types to profile configuration attributes
        config_mapping = {
            'story_picker': profile.agents_config.story_picker,
            'search_terms': profile.agents_config.search_terms,
            'data_extractor': profile.agents_config.data_extractor,
            'script_writer': profile.agents_config.script_writer,
            'youtube_metadata': profile.agents_config.youtube_metadata,
        }
        
        return config_mapping.get(agent_type)
    
    def create_all_agents(
        self, 
        profile: VideoProfile,
        session_data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, BaseAgent]:
        """
        Create all available agents for a profile
        
        Args:
            profile: Video profile configuration
            session_data: Optional session data for agent contexts
            
        Returns:
            Dictionary mapping agent types to agent instances
            
        Raises:
            AgentFactoryError: If any agent creation fails
        """
        agents = {}
        
        for agent_type in self._agent_registry.keys():
            try:
                agent = self.create_agent(agent_type, profile, session_data)
                agents[agent_type] = agent
            except Exception as e:
                error_msg = f"Failed to create {agent_type} agent: {str(e)}"
                self.logger.error(error_msg)
                raise AgentFactoryError(error_msg)
        
        self.logger.info(f"Created {len(agents)} agents for profile '{profile.profile_info.name}'")
        return agents
    
    def register_agent(self, agent_type: str, agent_class: Type[BaseAgent]) -> None:
        """
        Register a new agent type
        
        Args:
            agent_type: Unique identifier for the agent type
            agent_class: Agent class that inherits from BaseAgent
            
        Raises:
            AgentFactoryError: If registration fails
        """
        try:
            # Validate agent class
            if not issubclass(agent_class, BaseAgent):
                raise AgentFactoryError(f"Agent class must inherit from BaseAgent")
            
            # Register the agent
            self._agent_registry[agent_type] = agent_class
            
            self.logger.info(f"Registered agent type '{agent_type}'")
            
        except Exception as e:
            error_msg = f"Failed to register agent type '{agent_type}': {str(e)}"
            self.logger.error(error_msg)
            raise AgentFactoryError(error_msg)
    
    def get_available_agent_types(self) -> list[str]:
        """
        Get list of available agent types
        
        Returns:
            List of registered agent type names
        """
        return list(self._agent_registry.keys())
    
    def validate_profile_for_agents(self, profile: VideoProfile) -> tuple[bool, list[str]]:
        """
        Validate that a profile has all required agent configurations
        
        Args:
            profile: Video profile to validate
            
        Returns:
            Tuple of (is_valid, list_of_issues)
        """
        issues = []
        
        # Check that agents_config exists
        if not hasattr(profile, 'agents_config') or profile.agents_config is None:
            issues.append("Profile missing agents_config")
            return False, issues
        
        # Check each required agent configuration
        for agent_type in self._agent_registry.keys():
            config = self._get_agent_config(agent_type, profile)
            if config is None:
                issues.append(f"Missing configuration for {agent_type} agent")
        
        return len(issues) == 0, issues


# Global factory instance
_factory_instance = None

def get_agent_factory() -> AgentFactory:
    """
    Get the global agent factory instance
    
    Returns:
        AgentFactory instance
    """
    global _factory_instance
    if _factory_instance is None:
        _factory_instance = AgentFactory()
    return _factory_instance


def create_agent(
    agent_type: str, 
    profile: VideoProfile,
    session_data: Optional[Dict[str, Any]] = None
) -> BaseAgent:
    """
    Convenience function to create an agent using the global factory
    
    Args:
        agent_type: Type of agent to create
        profile: Video profile configuration
        session_data: Optional session data
        
    Returns:
        Configured agent instance
    """
    factory = get_agent_factory()
    return factory.create_agent(agent_type, profile, session_data)


def create_all_agents(
    profile: VideoProfile,
    session_data: Optional[Dict[str, Any]] = None
) -> Dict[str, BaseAgent]:
    """
    Convenience function to create all agents using the global factory
    
    Args:
        profile: Video profile configuration
        session_data: Optional session data
        
    Returns:
        Dictionary mapping agent types to agent instances
    """
    factory = get_agent_factory()
    return factory.create_all_agents(profile, session_data)

#!/usr/bin/env python3
"""
Simple Profile Creator

Creates new video profiles without requiring external dependencies.
"""

import json
from pathlib import Path

def create_profile():
    """Create a new profile interactively"""
    print("🎯 Creating New Profile")
    print("=" * 40)
    
    # Get basic information
    name = input("Profile name: ").strip()
    print("Content type options: finance, gaming_news, ai_news, tech_news, other")
    profile_type = input("Content type: ").strip()
    channel_name = input("YouTube channel name: ").strip()
    description = input("Profile description: ").strip()
    target_audience = input("Target audience: ").strip()
    
    # Create basic profile structure
    profile_data = {
        "profile_info": {
            "name": name,
            "type": profile_type,
            "channel_name": channel_name,
            "description": description,
            "target_audience": target_audience,
            "created_date": "2024-01-01",
            "version": "1.0.0"
        },
        "content_config": {
            "keywords": [],
            "hashtags": [],
            "content_style": "educational",
            "tone": "professional",
            "video_length_target": 45,
            "scenes_per_video": 5
        },
        "data_sources": {
            "news_sources": [],
            "trends_config": {
                "category": "business_industrial",
                "region": "united_states",
                "keywords": [],
                "timeframe": "now 1-d"
            },
            "search_config": {
                "safe_search": "off",
                "time_limit_hours": 48,
                "max_results": 5
            }
        },
        "agents_config": {
            "story_picker": {
                "system_prompt": f"You are a content selector for the YouTube channel {channel_name}. Your job is to create 3 {profile_type}-related video ideas based on provided trends and current news.",
                "rules": [],
                "max_topics": 3
            },
            "search_terms": {
                "system_prompt": f"You are a research assistant for '{channel_name},' a YouTube channel focused on {profile_type}. Generate three distinct search terms based on a given topic to find diverse and reliable sources.",
                "terms_per_topic": 3
            },
            "script_writer": {
                "system_prompt": f"You are a professional scriptwriter for '{channel_name},' a fast-paced YouTube Shorts channel focused on {profile_type}. Your scripts should be engaging, educational, and tailored for a short-form video format.",
                "temperature": 0.7,
                "style_guidelines": []
            },
            "metadata_generator": {
                "system_prompt": f"Generate optimized YouTube metadata for {profile_type} content YouTube Shorts. Focus on SEO optimization while maintaining accuracy.",
                "title_templates": [],
                "description_template": "{{description}}\\n\\n🔔 Subscribe for daily insights!\\n💡 Like if this helped you!\\n📝 Share your thoughts in the comments!\\n\\n#shorts #{profile_type.replace('_', '')}"
            }
        },
        "upload_config": {
            "browser_profile": {
                "name": f"{name}_bot",
                "headless": False,
                "timeout": 60
            },
            "youtube_settings": {
                "privacy": "unlisted",
                "category": "Education",
                "made_for_kids": False,
                "enable_comments": True,
                "enable_ratings": True,
                "upload_schedule": "immediate"
            }
        },
        "media_config": {
            "tts": {
                "voice": "af_bella",
                "speed": 1.0,
                "language": "en"
            },
            "images": {
                "style": "photorealistic",
                "aspect_ratio": "9:16"
            },
            "video": {
                "resolution": "1080x1920",
                "fps": 30,
                "format": "mp4"
            }
        }
    }
    
    # Save profile
    try:
        profile_path = Path(f"profiles/examples/{name}_profile.json")
        profile_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(profile_path, 'w', encoding='utf-8') as f:
            json.dump(profile_data, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Profile created: {profile_path}")
        print("💡 Edit the file to customize RSS feeds, keywords, and other settings")
        
        # Show next steps
        print()
        print("Next steps:")
        print(f"1. Test the profile: python workflow.py --run --profile {name}")
        print(f"2. List profiles: python workflow.py --list-profiles")
        print(f"3. Edit the profile file to customize settings")
        
    except Exception as e:
        print(f"❌ Error creating profile: {e}")

def main():
    """Main entry point"""
    create_profile()

if __name__ == "__main__":
    main()

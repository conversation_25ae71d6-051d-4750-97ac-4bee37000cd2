#!/usr/bin/env python3
"""
Test the subprocess script directly
"""

import subprocess
import json
import sys
from pathlib import Path

def test_subprocess_direct():
    """Test the subprocess script directly"""
    try:
        print("🔍 Testing subprocess script directly...")
        
        # Get the path to our timeout script
        script_path = Path(__file__).parent / "Helpers" / "llm_with_timeout.py"
        
        print(f"📁 Script path: {script_path}")
        print(f"📁 Script exists: {script_path.exists()}")
        
        if not script_path.exists():
            print("❌ Script not found!")
            return False
        
        # Prepare the prompt data as JSON
        prompt_data = {
            "prompt": "Say 'Hello World' and nothing else.",
            "model_name": "gemini-1.5-flash",
            "temperature": 0.7,
            "max_tokens": 100
        }
        
        print("🔍 Testing subprocess with simple prompt...")
        
        # Run the subprocess with timeout
        result = subprocess.run(
            [sys.executable, str(script_path), json.dumps(prompt_data)],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        print(f"📊 Return code: {result.returncode}")
        print(f"📊 Stdout: {result.stdout}")
        print(f"📊 Stderr: {result.stderr}")
        
        if result.returncode != 0:
            print(f"❌ Subprocess failed with return code {result.returncode}")
            return False
        
        # Parse the JSON response
        try:
            response_data = json.loads(result.stdout)
            print(f"📊 Response data: {response_data}")
            
            if "error" in response_data:
                print(f"❌ LLM request failed: {response_data['error']}")
                return False
            
            if "text" not in response_data:
                print("❌ No text in response")
                return False
            
            print(f"✅ Success! Response: {response_data['text']}")
            return True
            
        except json.JSONDecodeError as e:
            print(f"❌ Failed to parse response JSON: {str(e)}")
            return False
        
    except subprocess.TimeoutExpired:
        print("❌ Subprocess timed out")
        return False
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_subprocess_direct()
    sys.exit(0 if success else 1)

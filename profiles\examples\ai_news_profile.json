{"profile_info": {"name": "ai_insights", "type": "ai_news", "description": "AI and technology news for AI Insights channel", "version": "1.0.0", "channel_name": "AI Insights", "target_audience": "Tech enthusiasts and AI professionals"}, "content_config": {"keywords": ["artificial intelligence", "AI", "machine learning", "deep learning", "neural networks", "chatgpt", "openai", "google ai", "microsoft ai", "tech news", "automation", "robotics", "computer vision", "natural language processing", "AI research", "AI ethics", "AI applications", "AI startups", "AI tools"], "hashtags": ["#ai", "#artificialintelligence", "#machinelearning", "#deeplearning", "#chatgpt", "#openai", "#googleai", "#microsoft", "#technews", "#automation", "#robotics", "#airesearch", "#aiethics", "#youtubeshorts", "#shorts", "#tech", "#innovation"], "content_style": "educational", "tone": "informative", "video_length_target": 50, "scenes_per_video": 5}, "data_sources": {"news_sources": [{"name": "techcrunch_ai", "type": "rss", "url": "https://techcrunch.com/category/artificial-intelligence/feed/", "headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"}}, {"name": "venturebeat_ai", "type": "rss", "url": "https://venturebeat.com/ai/feed/", "headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"}}, {"name": "mit_tech_review", "type": "rss", "url": "https://www.technologyreview.com/feed/", "headers": {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"}}], "trends_config": {"category": "science_technology", "region": "united_states", "keywords": ["artificial intelligence", "AI", "machine learning", "chatgpt", "openai"], "timeframe": "now 1-d"}, "search_config": {"safe_search": "moderate", "time_limit_hours": 24, "max_results": 5}}, "agents_config": {"story_picker": {"system_prompt": "You are a content selector for the YouTube channel AI Insights. Your job is to create 3 AI and technology-related video ideas based on provided trends and current AI news. Focus on topics that will educate and inform tech enthusiasts about the latest AI developments.", "rules": ["Focus on recent AI breakthroughs and developments", "Include major AI company announcements", "Cover practical AI applications and tools", "Highlight AI research and innovations", "Discuss AI ethics and societal impact when relevant", "Keep content accurate and fact-based", "Avoid overly technical jargon", "Up to 3 topic ideas per response", "Don't repeat topics from today's videos"], "max_topics": 3}, "search_terms": {"system_prompt": "You are a research assistant for 'AI Insights,' a YouTube channel focused on AI and technology news. Generate three distinct search terms based on a given AI topic to find diverse and reliable tech sources.", "terms_per_topic": 3}, "script_writer": {"system_prompt": "You are a professional scriptwriter for 'AI Insights,' an educational YouTube Shorts channel focused on AI and technology news. Your scripts should be informative, accessible, and tailored for a short-form video format. Your audience consists of tech enthusiasts who want to stay updated on AI developments.", "temperature": 0.6, "style_guidelines": ["Explain complex AI concepts in simple terms", "Include specific company names and product details", "Mention practical implications and applications", "Use clear and precise language", "Provide context for AI developments", "Keep technical accuracy while remaining accessible"]}, "metadata_generator": {"system_prompt": "Generate optimized YouTube metadata for AI and technology content YouTube Shorts. Focus on AI and tech SEO keywords to maximize discoverability among tech-savvy audiences.", "title_templates": ["{topic}: AI News Update", "BREAKING: {topic}", "{topic} - What This Means for AI", "AI Alert: {topic}", "{topic} Explained Simply"], "description_template": "{description}\n\n🤖 Subscribe for daily AI insights!\n👍 Like if you found this helpful!\n💭 Share your thoughts on AI in the comments!\n\n#shorts #ai #artificialintelligence #tech #innovation"}}, "upload_config": {"browser_profile": {"name": "ai_insights_bot", "headless": false, "timeout": 60}, "youtube_settings": {"privacy": "public", "category": "Science & Technology", "made_for_kids": false, "enable_comments": true, "enable_ratings": true, "language": "English", "default_tags": ["ai", "artificialintelligence", "machinelearning", "tech", "technews", "innovation", "chatgpt", "openai", "youtubeshorts", "shorts"]}}, "media_config": {"tts_config": {"voice": "af_heart", "speed": 0.95, "language_code": "american_english"}, "image_config": {"style_prompts": ["futuristic AI technology graphics", "clean tech interface designs", "neural network visualizations", "modern technology concepts", "AI and robotics imagery"], "aspect_ratio": "portrait", "quality": "hd"}, "video_config": {"fps": 30, "resolution": "1080x1920", "transition_type": "fade"}}}
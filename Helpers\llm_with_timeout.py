#!/usr/bin/env python3
"""
Standalone script to make LLM requests with process-level timeout.
This script is called as a subprocess to avoid blocking issues.
"""

import sys
import json
import os
import google.generativeai as genai
from pathlib import Path

def load_api_key():
    """Load API key from environment or .env file"""
    # Try environment variable first
    api_key = os.getenv('GEMINI_API_KEY')
    if api_key:
        return api_key
    
    # Try loading from .env file
    try:
        from dotenv import load_dotenv
        env_path = Path.cwd() / '.env'
        if env_path.exists():
            load_dotenv(env_path)
            return os.getenv('GEMINI_API_KEY')
    except ImportError:
        pass
    
    return None

def make_llm_request(prompt, model_name="gemini-1.5-flash", temperature=0.7, max_tokens=2048):
    """Make a single LLM request"""
    try:
        # Load and configure API key
        api_key = load_api_key()
        if not api_key:
            return {"error": "GEMINI_API_KEY not found in environment variables or .env file"}
        
        genai.configure(api_key=api_key)
        
        # Configure the model
        generation_config = {
            "temperature": temperature,
            "max_output_tokens": max_tokens,
        }
        
        model = genai.GenerativeModel(
            model_name=model_name,
            generation_config=generation_config
        )
        
        # Make the request
        response = model.generate_content(prompt)
        
        if response.text:
            return {"success": True, "text": response.text}
        else:
            return {"error": "No response text generated"}
            
    except Exception as e:
        return {"error": f"LLM request failed: {str(e)}"}

def main():
    """Main function to handle command line arguments"""
    if len(sys.argv) != 2:
        print(json.dumps({"error": "Usage: python llm_with_timeout.py '<prompt_json>'"}))
        sys.exit(1)
    
    try:
        # Parse the JSON prompt data
        prompt_data = json.loads(sys.argv[1])
        prompt = prompt_data.get("prompt", "")
        model_name = prompt_data.get("model_name", "gemini-1.5-flash")
        temperature = prompt_data.get("temperature", 0.7)
        max_tokens = prompt_data.get("max_tokens", 2048)
        
        # Make the request
        result = make_llm_request(prompt, model_name, temperature, max_tokens)
        
        # Output the result as JSON
        print(json.dumps(result))
        
    except json.JSONDecodeError as e:
        print(json.dumps({"error": f"Invalid JSON input: {str(e)}"}))
        sys.exit(1)
    except Exception as e:
        print(json.dumps({"error": f"Unexpected error: {str(e)}"}))
        sys.exit(1)

if __name__ == "__main__":
    main()

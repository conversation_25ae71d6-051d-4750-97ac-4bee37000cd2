from Helpers.LLMRequest import create_llm_client, ModelType

def SearchTermAgent(topic):
    llm = create_llm_client(
        system_instruction="""You are a research assistant for "Liquid Trading," a YouTube channel focused on financial news and analysis.
Your task is to generate three distinct search terms based on a given topic to find diverse and reliable sources.

The channel provides quick insights into trending financial topics to help viewers make informed decisions. The main focus is the stock and crypto markets.

Generate one search term for each of the following categories:
1.  **News:** To find the latest news articles and reports.
2.  **Data:** To find quantitative data, charts, or statistics.
3.  **Analysis:** To find expert opinions, analysis, or discussions.

## Response Format
<terms><term>News-related search term</term><term>Data-related search term</term><term>Analysis-related search term</term></terms>
""",
        enable_tools=False,
        model=ModelType.GEMINI_2_5_FLASH_LIGHT,
        enable_training_logging=True,
        agent_prefix="SearchTerms"
    )

    response = llm.generate_response(f"""You should make 3 search terms.

Topic to create search terms for:
{topic}
""")
    if response is None:
        return []

    # Parse response
    split_one = response.split("<terms>")[1]
    split_two = split_one.split("</terms>")[0]
    split_three = split_two.split("<term>")[1:]
    terms = [split_three[i].split("</term>")[0] for i in range(len(split_three))]

    return terms

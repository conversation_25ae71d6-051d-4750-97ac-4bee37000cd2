from typing import List
import xml.etree.ElementTree as ET

from Helpers.LLMRequest import create_llm_client, ModelType
from Helpers.RetryMechanism import create_llm_retry_mechanism, RetryError
from .BaseAgent import BaseAgent, AgentContext


class SearchTermsAgent(BaseAgent):
    """
    Profile-aware search terms agent for generating research queries

    Features:
    - Profile-specific search term generation
    - Configurable search categories and approaches
    - Support for different content types and audiences
    - Retry mechanism for reliability
    """

    def __init__(self, context: AgentContext):
        """Initialize the search terms agent"""
        super().__init__(context, model=ModelType.GEMINI_2_5_FLASH_LIGHT)

    def execute(self, topic: str) -> List[str]:
        """
        Generate search terms for a given topic

        Args:
            topic: The topic to generate search terms for

        Returns:
            List of search terms for research
        """
        self._log_execution_start("search_term_generation", topic=topic)

        try:
            # Validate input
            self._validate_input(topic, "topic")

            # Build the prompt with profile context
            prompt = self._build_search_terms_prompt(topic)

            # Initialize retry mechanism
            retry_mechanism = create_llm_retry_mechanism()

            def generate_terms_with_retry():
                return self.llm.generate_response(prompt, add_to_history=False)

            # Use retry mechanism for LLM call
            response = retry_mechanism.retry_operation(
                generate_terms_with_retry,
                exception_types=(Exception,),
                operation_name="search_term_generation"
            )

            # Parse the response
            terms = self._parse_terms_response(response)

            self._log_execution_end("search_term_generation", terms)
            return terms

        except RetryError as e:
            self.logger.error(f"Search term generation failed after {e.attempt_count} attempts: {e.last_exception}")
            return []
        except Exception as e:
            self._handle_llm_error("search_term_generation", e)
            return []

    def _build_search_terms_prompt(self, topic: str) -> str:
        """Build the prompt for search term generation"""

        # Get terms per topic from agent config
        terms_per_topic = getattr(self.agent_config, 'terms_per_topic', 3)

        # Build the prompt
        prompt = f"""# Research Assistant for {self.get_channel_name()}

## Task Information
You are a research assistant for "{self.get_channel_name()}," a YouTube channel focused on {self.profile.profile_info.type}.
Your task is to generate {terms_per_topic} distinct search terms based on a given topic to find diverse and reliable sources.

## Channel Context
- Target audience: {self.get_target_audience()}
- Content style: {self.get_content_style()}
- Content tone: {self.get_content_tone()}
- Channel focus: {self.profile.profile_info.description}

## Search Term Categories
Generate one search term for each of the following categories:
1. **News:** To find the latest news articles and reports
2. **Data:** To find quantitative data, charts, or statistics
3. **Analysis:** To find expert opinions, analysis, or discussions

{self.format_rules_for_prompt()}

## Topic to Research
{topic}

## Output Format
<terms><term>News-related search term</term><term>Data-related search term</term><term>Analysis-related search term</term></terms>
"""

        return prompt

    def _parse_terms_response(self, response: str) -> List[str]:
        """Parse the LLM response to extract search terms"""
        if not response:
            return []

        try:
            # Try XML parsing first
            if "<terms>" in response and "</terms>" in response:
                terms_section = response.split("<terms>")[1].split("</terms>")[0]

                # Extract individual terms
                term_matches = []
                if "<term>" in terms_section:
                    term_parts = terms_section.split("<term>")[1:]
                    for part in term_parts:
                        if "</term>" in part:
                            term = part.split("</term>")[0].strip()
                            if term:
                                term_matches.append(term)

                return term_matches

            # Fallback: try to extract terms from numbered list
            lines = response.split('\n')
            terms = []
            for line in lines:
                line = line.strip()
                if line and (line.startswith('1.') or line.startswith('2.') or line.startswith('3.') or
                           line.startswith('-') or line.startswith('•')):
                    # Clean up the term
                    term = line.lstrip('123.-• ').strip()
                    if term:
                        terms.append(term)

            return terms[:3]  # Limit to 3 terms

        except Exception as e:
            self.logger.error(f"Failed to parse terms response: {e}")
            return []


# Legacy function for backward compatibility
def SearchTermAgent(topic):
    """Legacy function for backward compatibility"""
    llm = create_llm_client(
        system_instruction="""You are a research assistant for "Liquid Trading," a YouTube channel focused on financial news and analysis.
Your task is to generate three distinct search terms based on a given topic to find diverse and reliable sources.

The channel provides quick insights into trending financial topics to help viewers make informed decisions. The main focus is the stock and crypto markets.

Generate one search term for each of the following categories:
1.  **News:** To find the latest news articles and reports.
2.  **Data:** To find quantitative data, charts, or statistics.
3.  **Analysis:** To find expert opinions, analysis, or discussions.

## Response Format
<terms><term>News-related search term</term><term>Data-related search term</term><term>Analysis-related search term</term></terms>
""",
        enable_tools=False,
        model=ModelType.GEMINI_2_5_FLASH_LIGHT,
        enable_training_logging=True,
        agent_prefix="SearchTerms"
    )

    response = llm.generate_response(f"""You should make 3 search terms.

Topic to create search terms for:
{topic}
""")
    if response is None:
        return []

    # Parse response
    try:
        split_one = response.split("<terms>")[1]
        split_two = split_one.split("</terms>")[0]
        split_three = split_two.split("<term>")[1:]
        terms = [split_three[i].split("</term>")[0] for i in range(len(split_three))]
        return terms
    except:
        return []

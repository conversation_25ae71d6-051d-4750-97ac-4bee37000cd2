import json
import re
import logging
import sys
import os
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from Helpers.LLMRequest import LLMRequest, LLMConfig, ModelType
from Agents.ScriptWriter import Script


@dataclass
class YouTubeMetadata:
    """Represents YouTube metadata for a video"""
    title: str
    tags: List[str]
    description: str
    
    def to_dict(self) -> Dict:
        """Convert metadata to dictionary format"""
        return {
            "title": self.title,
            "tags": self.tags,
            "description": self.description
        }
    
    def to_json(self) -> str:
        """Convert metadata to JSON string"""
        return json.dumps(self.to_dict(), indent=2)


class YouTubeMetadataError(Exception):
    """Custom exception for YouTube metadata generation errors"""
    pass


class YouTubeMetadataAgent:
    """
    Agent for generating optimized YouTube metadata from video scripts
    
    Features:
    - Generate compelling titles optimized for YouTube Shorts and financial content
    - Create relevant tags for financial/investment content discovery
    - Write engaging descriptions that encourage engagement
    - Support for different content styles (educational, news, analysis)
    - Proper JSON output format for metadata
    - Integration with existing LLMRequest helper
    - Logging and error handling with custom exceptions
    """
    
    def __init__(self, api_key: Optional[str] = None, model: ModelType = ModelType.GEMINI_2_5_FLASH):
        """
        Initialize the YouTubeMetadata agent
        
        Args:
            api_key: Gemini API key (optional, will use environment variable if not provided)
            model: Model type to use for generation
        """
        self.logger = logging.getLogger(__name__)
        
        self.llm_config = LLMConfig(
            model=model,
            temperature=0.8,
            system_instruction="""You are a YouTube SEO expert specializing in financial content and YouTube Shorts optimization.

            Your expertise includes:
            - Creating click-worthy titles that drive engagement
            - Optimizing for YouTube Shorts algorithm
            - Financial content SEO and trending keywords
            - Viewer psychology and engagement tactics
            - YouTube best practices for discovery

            CRITICAL REQUIREMENTS:
            1. Titles MUST be under 100 characters for mobile optimization
            2. Focus on financial keywords that trend on YouTube
            3. Use power words that create urgency/curiosity
            4. Tags should include mix of broad and specific financial terms
            5. Descriptions should encourage engagement (likes, comments, shares)

            Always respond with the following JSON format:
            {
                "title": "Your optimized title here",
                "tags": ["tag1", "tag2", "tag3", "tag4", "tag5"],
                "description": "Your engaging description here"
            }

            Make content discovery-optimized and engagement-focused.""",
            enable_training_logging=True,
            agent_prefix="YouTubeMetadata"
        )
        
        self.llm = LLMRequest(api_key=api_key, config=self.llm_config)
        
        # Financial keywords and hashtags commonly used in YouTube
        self.financial_keywords = [
            "stocks", "investing", "crypto", "bitcoin", "ethereum", "trading", "market",
            "finance", "money", "portfolio", "dividends", "options", "forex", "news",
            "analysis", "profit", "passive income", "wealth", "financial freedom",
            "stock market", "crypto news", "investment tips", "day trading", "swing trading"
        ]
        
        self.trending_hashtags = [
            "#stocks", "#investing", "#crypto", "#bitcoin", "#ethereum", "#trading",
            "#finance", "#money", "#stockmarket", "#investment", "#financialfreedom",
            "#passiveincome", "#wealth", "#marketanalysis", "#cryptonews", "#stocknews",
            "#youtubeshorts", "#shorts", "#financetips", "#investmenttips"
        ]
    
    def generate_metadata(
        self, 
        script: Script, 
        topic: str, 
        content_style: str = "educational"
    ) -> YouTubeMetadata:
        """
        Generate optimized YouTube metadata from a script
        
        Args:
            script: Script object containing video content
            topic: Main topic/subject of the video
            content_style: Style of content ("educational", "news", "analysis")
            
        Returns:
            YouTubeMetadata object with optimized title, tags, and description
            
        Raises:
            YouTubeMetadataError: If metadata generation fails
        """
        try:
            # Extract script content for context
            script_text = self._extract_script_text(script)
            
            # Generate metadata using LLM
            prompt = self._build_metadata_prompt(script_text, topic, content_style)
            response = self.llm.generate_response(prompt, add_to_history=False)
            
            # Parse and validate the response
            metadata = self._parse_metadata_response(response)
            
            # Enhance with additional optimization
            metadata = self._enhance_metadata(metadata, topic, content_style)
            
            self.logger.info(f"Successfully generated metadata for topic: {topic}")
            return metadata
            
        except Exception as e:
            error_msg = f"Failed to generate YouTube metadata: {str(e)}"
            self.logger.error(error_msg)
            raise YouTubeMetadataError(error_msg)
    
    def _extract_script_text(self, script: Script) -> str:
        """
        Extract text content from script for metadata generation
        
        Args:
            script: Script object
            
        Returns:
            Combined text content from all scenes
        """
        script_text = ""
        for scene in script.scenes:
            script_text += scene.text + " "
        return script_text.strip()
    
    def _build_metadata_prompt(self, script_text: str, topic: str, content_style: str) -> str:
        """
        Build the prompt for metadata generation
        
        Args:
            script_text: Combined script text
            topic: Video topic
            content_style: Content style
            
        Returns:
            Formatted prompt string
        """
        return f"""Generate optimized YouTube metadata for a financial content YouTube Short.

CONTENT DETAILS:
Topic: {topic}
Content Style: {content_style}
Script Text: {script_text}

OPTIMIZATION REQUIREMENTS:

TITLE (Max 100 characters):
- Use power words that create urgency/curiosity
- Include main financial keyword from topic
- Optimize for YouTube Shorts discovery
- Examples of good financial titles:
  * "Why Tesla Stock CRASHED Today! 📉"
  * "Bitcoin's SHOCKING New Price Target! 🚀"
  * "This Stock Could Make You RICH! 💰"
  * "Market Crash Coming? Here's What to Do! ⚠️"

TAGS (5-8 tags):
- Mix of broad financial terms and specific keywords
- Include trending financial hashtags
- YouTube Shorts optimization tags
- Related financial topics for discovery

DESCRIPTION:
- Hook viewers in first line
- Include relevant hashtags
- Call-to-action for engagement
- Encourage likes, comments, subscribes
- Include disclaimers if needed
- Use emojis strategically
- 2-3 short paragraphs maximum

Focus on financial content that attracts viewers interested in {content_style} financial information.
Optimize for maximum discovery and engagement on YouTube Shorts platform.

Remember: Respond ONLY with valid JSON format as specified in your instructions."""

    def _parse_metadata_response(self, response: str) -> YouTubeMetadata:
        """
        Parse the LLM response containing JSON metadata
        
        Args:
            response: Raw LLM response containing JSON
            
        Returns:
            YouTubeMetadata object
            
        Raises:
            YouTubeMetadataError: If parsing fails
        """
        try:
            # Clean the response
            cleaned_response = self._clean_json_response(response)
            
            # Parse JSON
            metadata_dict = json.loads(cleaned_response)
            
            # Validate required fields
            required_fields = ["title", "tags", "description"]
            for field in required_fields:
                if field not in metadata_dict:
                    raise YouTubeMetadataError(f"Missing required field: {field}")
            
            # Validate title length
            title = metadata_dict["title"]
            if len(title) > 100:
                self.logger.warning(f"Title too long ({len(title)} chars), truncating...")
                title = title[:97] + "..."
            
            # Ensure tags is a list
            tags = metadata_dict["tags"]
            if not isinstance(tags, list):
                raise YouTubeMetadataError("Tags must be a list")
            
            return YouTubeMetadata(
                title=title,
                tags=tags,
                description=metadata_dict["description"]
            )
            
        except json.JSONDecodeError as e:
            raise YouTubeMetadataError(f"Invalid JSON response: {str(e)}")
        except Exception as e:
            raise YouTubeMetadataError(f"Failed to parse metadata response: {str(e)}")
    
    def _clean_json_response(self, response: str) -> str:
        """
        Clean the response to extract valid JSON
        
        Args:
            response: Raw response text
            
        Returns:
            Cleaned JSON string
        """
        # Remove markdown code blocks
        response = re.sub(r'```[a-zA-Z]*\n?', '', response)
        response = re.sub(r'```', '', response)
        
        # Find JSON content between braces
        json_match = re.search(r'\{.*\}', response, re.DOTALL)
        if json_match:
            return json_match.group(0)
        
        return response.strip()
    
    def _enhance_metadata(self, metadata: YouTubeMetadata, topic: str, content_style: str) -> YouTubeMetadata:
        """
        Enhance metadata with additional optimizations
        
        Args:
            metadata: Initial metadata object
            topic: Video topic
            content_style: Content style
            
        Returns:
            Enhanced YouTubeMetadata object
        """
        # Ensure essential financial tags are included
        enhanced_tags = list(metadata.tags)
        
        # Add relevant financial keywords if missing
        topic_lower = topic.lower()
        for keyword in self.financial_keywords:
            if keyword in topic_lower and keyword not in [tag.lower() for tag in enhanced_tags]:
                enhanced_tags.append(keyword)
        
        # Add YouTube Shorts optimization tags
        shorts_tags = ["youtubeshorts", "shorts", "finance", "investing"]
        for tag in shorts_tags:
            if tag not in [t.lower() for t in enhanced_tags]:
                enhanced_tags.append(tag)
        
        # Limit to reasonable number of tags (8-12 is optimal)
        enhanced_tags = enhanced_tags[:12]
        
        # Enhance description with trending hashtags if not present
        description = metadata.description
        if not any(hashtag in description for hashtag in self.trending_hashtags[:5]):
            # Add a few relevant hashtags
            relevant_hashtags = [
                "#shorts", "#finance", "#investing", "#stockmarket", "#money"
            ]
            description += f"\n\n{' '.join(relevant_hashtags)}"
        
        return YouTubeMetadata(
            title=metadata.title,
            tags=enhanced_tags,
            description=description
        )
    
    def validate_metadata(self, metadata: YouTubeMetadata) -> Tuple[bool, List[str]]:
        """
        Validate generated metadata for YouTube compliance
        
        Args:
            metadata: YouTubeMetadata object to validate
            
        Returns:
            Tuple of (is_valid, list_of_issues)
        """
        issues = []
        
        # Validate title
        if not metadata.title.strip():
            issues.append("Title is empty")
        elif len(metadata.title) > 100:
            issues.append(f"Title too long: {len(metadata.title)} characters (max 100)")
        
        # Validate tags
        if not metadata.tags:
            issues.append("No tags provided")
        elif len(metadata.tags) > 15:
            issues.append(f"Too many tags: {len(metadata.tags)} (YouTube allows 15 max)")
        
        for i, tag in enumerate(metadata.tags):
            if len(tag) > 30:
                issues.append(f"Tag {i+1} too long: '{tag}' ({len(tag)} chars, max 30)")
        
        # Validate description
        if not metadata.description.strip():
            issues.append("Description is empty")
        elif len(metadata.description) > 5000:
            issues.append(f"Description too long: {len(metadata.description)} characters (max 5000)")
        
        # Check for financial content indicators
        content_lower = (metadata.title + " " + " ".join(metadata.tags) + " " + metadata.description).lower()
        financial_indicators = ["stock", "invest", "crypto", "bitcoin", "trading", "market", "finance", "money"]
        if not any(indicator in content_lower for indicator in financial_indicators):
            issues.append("No clear financial content indicators found")
        
        return len(issues) == 0, issues
    
    def generate_multiple_variants(
        self, 
        script: Script, 
        topic: str, 
        content_style: str = "educational",
        num_variants: int = 3
    ) -> List[YouTubeMetadata]:
        """
        Generate multiple metadata variants for A/B testing
        
        Args:
            script: Script object
            topic: Video topic
            content_style: Content style
            num_variants: Number of variants to generate
            
        Returns:
            List of YouTubeMetadata variants
        """
        variants = []
        
        for i in range(num_variants):
            try:
                # Slightly modify the prompt for variation
                variant_prompt = f"Generate variant #{i+1} with different approach: "
                
                # Generate variant
                metadata = self.generate_metadata(script, topic, content_style)
                variants.append(metadata)
                
                self.logger.info(f"Generated metadata variant {i+1}")
                
            except Exception as e:
                self.logger.warning(f"Failed to generate variant {i+1}: {str(e)}")
        
        return variants


# Example usage and testing
if __name__ == "__main__":
    import logging
    from Agents.ScriptWriter import Scene, Script
    
    # Set up logging
    logging.basicConfig(level=logging.INFO)
    
    # Create test script
    test_scenes = [
        Scene(
            text="Tesla stock crashed 15% today after Elon Musk's controversial Twitter announcement.",
            image_prompt="Tesla stock chart showing dramatic red decline"
        ),
        Scene(
            text="Investors are panicking as the electric vehicle market faces new regulatory challenges.",
            image_prompt="Worried investors looking at market data"
        ),
        Scene(
            text="But here's why this might actually be a buying opportunity for smart investors.",
            image_prompt="Green upward trending arrow with Tesla logo"
        )
    ]
    
    test_script = Script(scenes=test_scenes)
    test_topic = "Tesla Stock Crash Analysis"
    
    try:
        # Initialize the agent
        metadata_agent = YouTubeMetadataAgent()
        
        # Generate metadata
        print("Generating YouTube metadata...")
        metadata = metadata_agent.generate_metadata(
            script=test_script,
            topic=test_topic,
            content_style="news"
        )
        
        print("\n=== Generated YouTube Metadata ===")
        print(f"Title: {metadata.title}")
        print(f"Title Length: {len(metadata.title)} characters")
        print(f"\nTags: {', '.join(metadata.tags)}")
        print(f"Number of Tags: {len(metadata.tags)}")
        print(f"\nDescription:\n{metadata.description}")
        print(f"Description Length: {len(metadata.description)} characters")
        
        # Validate metadata
        is_valid, issues = metadata_agent.validate_metadata(metadata)
        print(f"\n=== Validation Results ===")
        print(f"Valid: {'✓' if is_valid else '✗'}")
        if issues:
            print("Issues found:")
            for issue in issues:
                print(f"- {issue}")
        
        # Export as JSON
        print(f"\n=== JSON Export ===")
        print(metadata.to_json())
        
        # Generate multiple variants
        print(f"\n=== Generating Multiple Variants ===")
        variants = metadata_agent.generate_multiple_variants(
            script=test_script,
            topic=test_topic,
            content_style="news",
            num_variants=2
        )
        
        for i, variant in enumerate(variants, 1):
            print(f"\nVariant {i} Title: {variant.title}")
    
    except YouTubeMetadataError as e:
        print(f"Metadata generation error: {e}")
    except Exception as e:
        print(f"Unexpected error: {e}")
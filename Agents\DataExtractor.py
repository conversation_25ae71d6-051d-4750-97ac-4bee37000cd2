from Helpers.LLMRequest import create_llm_client, ModelType
from Helpers.RetryMechanism import create_llm_retry_mechanism

def ExtractorAgent(topic, source_text):
    llm = create_llm_client(
        system_instruction=f"""You are an expert data extractor. Your job is to extract key information based on the topic from the provided text.
Present the extracted information in a clear, structured format using Markdown (e.g., headings, bullet points, bold text) to organize the data.

Topic: {topic}

## Response format if content found
<response>
## Key Findings on {topic}

- **Metric 1:** Value
- **Metric 2:** Value
- **Key Insight:** A brief summary of the important information.

*Additional details can be included here.*
</response>

## Response format if no helpful content found
<response>Not Found</response>

Ensure your entire response is enclosed within the <response> tag.
""",
        enable_tools=False,
        model=ModelType.GEMINI_2_5_FLASH,
        enable_training_logging=True,
        agent_prefix="DataExtractor"
    )

    try:
        # Initialize retry mechanism for more reliable LLM calls
        retry_mechanism = create_llm_retry_mechanism()

        def generate_extraction_with_retry():
            return llm.generate_response(f"""Topic: {topic}

Source Text:
{source_text}
""")

        # Use retry mechanism for LLM call
        response = retry_mechanism.retry_operation(
            generate_extraction_with_retry,
            exception_types=(Exception,),
            operation_name=f"content_extraction_{topic[:30]}"
        )

        if response is None:
            return None

        # Parse response with error handling
        if "<response>" not in response or "</response>" not in response:
            print(f"⚠️  Invalid response format from LLM for topic '{topic}'")
            return None

        split_one = response.split("<response>")[1]
        split_two = split_one.split("</response>")[0].strip()

        if split_two == "Not Found" or not split_two:
            return None

        return split_two

    except Exception as e:
        print(f"❌ Error in ExtractorAgent for topic '{topic}': {str(e)}")
        return None

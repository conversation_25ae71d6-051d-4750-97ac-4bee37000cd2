from typing import Optional
from Helpers.LLMRequest import create_llm_client, ModelType
from Helpers.RetryMechanism import create_llm_retry_mechanism, RetryError
from .BaseAgent import BaseAgent, AgentContext


class DataExtractorAgent(BaseAgent):
    """
    Profile-aware data extractor agent for extracting key information from source text

    Features:
    - Profile-specific extraction criteria
    - Configurable extraction templates and formats
    - Support for different content types and focus areas
    - Structured markdown output with validation
    """

    def __init__(self, context: AgentContext):
        """Initialize the data extractor agent"""
        super().__init__(context, model=ModelType.GEMINI_2_5_FLASH)

    def execute(self, topic: str, source_text: str) -> Optional[str]:
        """
        Extract key information from source text based on topic

        Args:
            topic: The topic to extract information about
            source_text: The source text to extract from

        Returns:
            Extracted information in markdown format, or None if no relevant content found
        """
        self._log_execution_start("data_extraction", topic=topic, source_length=len(source_text))

        try:
            # Validate inputs
            self._validate_input(topic, "topic")
            self._validate_input(source_text, "source_text")

            # Build the prompt with profile context
            prompt = self._build_extraction_prompt(topic, source_text)

            # Initialize retry mechanism
            retry_mechanism = create_llm_retry_mechanism()

            def extract_data_with_retry():
                return self.llm.generate_response(prompt, add_to_history=False)

            # Use retry mechanism for LLM call
            response = retry_mechanism.retry_operation(
                extract_data_with_retry,
                exception_types=(Exception,),
                operation_name=f"content_extraction_{topic[:30]}"
            )

            # Parse the response
            extracted_data = self._parse_extraction_response(response)

            self._log_execution_end("data_extraction", extracted_data)
            return extracted_data

        except RetryError as e:
            self.logger.error(f"Data extraction failed after {e.attempt_count} attempts: {e.last_exception}")
            return None
        except Exception as e:
            self._handle_llm_error("data_extraction", e)
            return None

    def _build_extraction_prompt(self, topic: str, source_text: str) -> str:
        """Build the prompt for data extraction"""

        # Get extraction criteria from agent config
        extraction_criteria = getattr(self.agent_config, 'extraction_criteria', [])

        # Build criteria section
        criteria_section = ""
        if extraction_criteria:
            criteria_list = "\n".join([f"- {criterion}" for criterion in extraction_criteria])
            criteria_section = f"\n## Extraction Criteria\n{criteria_list}\n"

        # Build the prompt
        prompt = f"""# Expert Data Extractor for {self.get_channel_name()}

## Channel Context
You are an expert data extractor for "{self.get_channel_name()}," a YouTube channel focused on {self.profile.profile_info.type}.
- Target audience: {self.get_target_audience()}
- Content style: {self.get_content_style()}
- Content tone: {self.get_content_tone()}
- Channel focus: {self.profile.profile_info.description}

## Task
Extract key information about "{topic}" from the provided source text.
Present the extracted information in a clear, structured format using Markdown to organize the data.

{criteria_section}{self.format_rules_for_prompt()}

## Topic
{topic}

## Source Text
{source_text}

## Response Format

### If relevant content is found:
<response>
## Key Findings on {topic}

- **Metric 1:** Value
- **Metric 2:** Value
- **Key Insight:** A brief summary of the important information.

*Additional details can be included here.*
</response>

### If no helpful content is found:
<response>Not Found</response>

Ensure your entire response is enclosed within the <response> tag.
Focus on extracting information that would be valuable for {self.get_content_style()} content about {self.profile.profile_info.type}.
"""

        return prompt

    def _parse_extraction_response(self, response: str) -> Optional[str]:
        """Parse the LLM response to extract the data"""
        if not response:
            return None

        try:
            # Parse response with error handling
            if "<response>" not in response or "</response>" not in response:
                self.logger.warning("Invalid response format from LLM")
                return None

            split_one = response.split("<response>")[1]
            split_two = split_one.split("</response>")[0].strip()

            if split_two == "Not Found" or not split_two:
                return None

            return split_two

        except Exception as e:
            self.logger.error(f"Failed to parse extraction response: {e}")
            return None


# Legacy function for backward compatibility
def ExtractorAgent(topic, source_text):
    llm = create_llm_client(
        system_instruction=f"""You are an expert data extractor. Your job is to extract key information based on the topic from the provided text.
Present the extracted information in a clear, structured format using Markdown (e.g., headings, bullet points, bold text) to organize the data.

Topic: {topic}

## Response format if content found
<response>
## Key Findings on {topic}

- **Metric 1:** Value
- **Metric 2:** Value
- **Key Insight:** A brief summary of the important information.

*Additional details can be included here.*
</response>

## Response format if no helpful content found
<response>Not Found</response>

Ensure your entire response is enclosed within the <response> tag.
""",
        enable_tools=False,
        model=ModelType.GEMINI_2_5_FLASH,
        enable_training_logging=True,
        agent_prefix="DataExtractor"
    )

    try:
        # Initialize retry mechanism for more reliable LLM calls
        retry_mechanism = create_llm_retry_mechanism()

        def generate_extraction_with_retry():
            return llm.generate_response(f"""Topic: {topic}

Source Text:
{source_text}
""")

        # Use retry mechanism for LLM call
        response = retry_mechanism.retry_operation(
            generate_extraction_with_retry,
            exception_types=(Exception,),
            operation_name=f"content_extraction_{topic[:30]}"
        )

        if response is None:
            return None

        # Parse response with error handling
        if "<response>" not in response or "</response>" not in response:
            print(f"⚠️  Invalid response format from LLM for topic '{topic}'")
            return None

        split_one = response.split("<response>")[1]
        split_two = split_one.split("</response>")[0].strip()

        if split_two == "Not Found" or not split_two:
            return None

        return split_two

    except Exception as e:
        print(f"❌ Error in ExtractorAgent for topic '{topic}': {str(e)}")
        return None

#!/usr/bin/env python3
"""
Profile Management CLI

Command-line interface for managing video creation profiles.
Provides tools for creating, editing, validating, and managing profiles.
"""

import argparse
import json
import sys
from pathlib import Path
from typing import Dict, Any

# Rich imports for beautiful console output
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.text import Text
from rich import box
from rich.prompt import Prompt, Confirm
from rich.syntax import Syntax

# Profile management imports
from Helpers.ProfileManager import ProfileManager, ProfileError
from Helpers.ProfileValidator import ProfileValidator, ValidationError

# Initialize rich console
console = Console()


def display_header():
    """Display a beautiful header for the profile CLI"""
    header_text = Text()
    header_text.append("⚙️ ", style="bold yellow")
    header_text.append("Profile Manager", style="bold cyan")
    header_text.append(" CLI", style="bold magenta")

    subtitle = Text("Manage video creation profiles", style="italic bright_blue")

    header_panel = Panel(
        Text.assemble(header_text, "\n", subtitle),
        box=box.DOUBLE,
        border_style="bright_cyan",
        padding=(1, 2)
    )

    console.print()
    console.print(header_panel)
    console.print()


def list_profiles():
    """List all available profiles"""
    try:
        profile_manager = ProfileManager()
        profiles = profile_manager.list_profiles()
        
        if profiles:
            table = Table(title="📋 Available Profiles", box=box.ROUNDED, border_style="cyan")
            table.add_column("Profile Name", style="bold cyan", no_wrap=True)
            table.add_column("Type", style="yellow")
            table.add_column("Channel", style="green")
            table.add_column("Description", style="white")
            table.add_column("Status", style="bold")
            
            for profile_name in profiles:
                try:
                    profile = profile_manager.load_profile(profile_name)
                    status = "✅ Valid"
                    
                    # Validate profile
                    validator = ProfileValidator()
                    is_valid, issues = validator.validate_profile(profile)
                    if not is_valid:
                        status = f"❌ {len(issues)} issues"
                    
                    table.add_row(
                        profile_name,
                        profile.profile_info.type,
                        profile.profile_info.channel_name,
                        profile.profile_info.description[:50] + "..." if len(profile.profile_info.description) > 50 else profile.profile_info.description,
                        status
                    )
                except Exception as e:
                    table.add_row(
                        profile_name,
                        "Unknown",
                        "Unknown",
                        f"Error: {str(e)[:40]}...",
                        "❌ Error"
                    )
            
            console.print(table)
        else:
            console.print("⚠️  [bold yellow]No profiles found")
            console.print("💡 Create profiles using --create or copy examples from profiles/examples/")
            
    except Exception as e:
        console.print(f"❌ [bold red]Error listing profiles: {e}")


def validate_profile(profile_name: str):
    """Validate a specific profile"""
    try:
        profile_manager = ProfileManager()
        profile = profile_manager.load_profile(profile_name)
        
        validator = ProfileValidator()
        is_valid, issues = validator.validate_profile(profile)
        
        if is_valid:
            console.print(f"✅ [bold green]Profile '{profile_name}' is valid")
        else:
            console.print(f"❌ [bold red]Profile '{profile_name}' has {len(issues)} validation issues:")
            console.print()
            
            for i, issue in enumerate(issues, 1):
                console.print(f"  {i}. {issue}")
            
            console.print()
            console.print("💡 Fix these issues to use the profile")
            
    except ProfileError as e:
        console.print(f"❌ [bold red]Profile error: {e}")
    except Exception as e:
        console.print(f"❌ [bold red]Unexpected error: {e}")


def show_profile(profile_name: str):
    """Show detailed information about a profile"""
    try:
        profile_manager = ProfileManager()
        profile = profile_manager.load_profile(profile_name)
        
        # Display profile info
        info_table = Table(title=f"📋 Profile: {profile_name}", box=box.ROUNDED, border_style="blue")
        info_table.add_column("Property", style="bold cyan", no_wrap=True)
        info_table.add_column("Value", style="white")
        
        info_table.add_row("Name", profile.profile_info.name)
        info_table.add_row("Type", profile.profile_info.type)
        info_table.add_row("Channel Name", profile.profile_info.channel_name)
        info_table.add_row("Description", profile.profile_info.description)
        info_table.add_row("Target Audience", profile.profile_info.target_audience)
        
        console.print(info_table)
        console.print()
        
        # Display data sources
        if hasattr(profile.data_sources, 'rss_feeds') and profile.data_sources.rss_feeds:
            feeds_table = Table(title="📰 RSS Feeds", box=box.ROUNDED, border_style="green")
            feeds_table.add_column("Name", style="bold green")
            feeds_table.add_column("URL", style="blue")
            
            for feed in profile.data_sources.rss_feeds:
                feeds_table.add_row(feed.name, feed.url)
            
            console.print(feeds_table)
            console.print()
        
        # Display validation status
        validator = ProfileValidator()
        is_valid, issues = validator.validate_profile(profile)
        
        if is_valid:
            console.print("✅ [bold green]Profile validation: PASSED")
        else:
            console.print(f"❌ [bold red]Profile validation: FAILED ({len(issues)} issues)")
            for issue in issues:
                console.print(f"  • {issue}")
                
    except ProfileError as e:
        console.print(f"❌ [bold red]Profile error: {e}")
    except Exception as e:
        console.print(f"❌ [bold red]Unexpected error: {e}")


def create_profile_interactive():
    """Create a new profile interactively"""
    console.print("🎯 [bold blue]Creating New Profile")
    console.print("=" * 40)
    
    # Get basic information
    name = Prompt.ask("Profile name")
    profile_type = Prompt.ask("Content type", choices=["finance", "gaming_news", "ai_news", "tech_news", "other"])
    channel_name = Prompt.ask("YouTube channel name")
    description = Prompt.ask("Profile description")
    target_audience = Prompt.ask("Target audience")
    
    # Create basic profile structure
    profile_data = {
        "profile_info": {
            "name": name,
            "type": profile_type,
            "channel_name": channel_name,
            "description": description,
            "target_audience": target_audience,
            "created_date": "2024-01-01",
            "version": "1.0"
        },
        "content_config": {
            "content_style": "educational",
            "content_tone": "professional",
            "video_length_seconds": 60,
            "scenes_per_video": 5,
            "creation_interval_hours": 2
        },
        "data_sources": {
            "rss_feeds": [],
            "google_trends": {
                "enabled": True,
                "region": "united_states",
                "category": "business_industrial",
                "count": 30
            },
            "search": {
                "max_results": 5,
                "time_limit_hours": 48
            }
        },
        "agents_config": {
            "story_picker": {
                "max_topics": 10,
                "avoid_duplicates": True,
                "rules": []
            },
            "search_terms": {
                "max_terms": 5,
                "include_synonyms": True,
                "focus_keywords": []
            },
            "data_extractor": {
                "extraction_criteria": []
            },
            "script_writer": {
                "style_guidelines": [],
                "template_phrases": []
            },
            "youtube_metadata": {
                "title_templates": [],
                "description_templates": [],
                "default_tags": []
            }
        },
        "upload_config": {
            "browser_profile": "default",
            "upload_schedule": "immediate",
            "privacy_setting": "public"
        },
        "media_config": {
            "tts": {
                "voice": "af_bella",
                "speed": 1.0,
                "language": "en"
            },
            "images": {
                "style": "photorealistic",
                "aspect_ratio": "9:16"
            },
            "video": {
                "resolution": "1080x1920",
                "fps": 30,
                "format": "mp4"
            }
        }
    }
    
    # Save profile
    try:
        profile_path = Path(f"profiles/examples/{name}_profile.json")
        profile_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(profile_path, 'w', encoding='utf-8') as f:
            json.dump(profile_data, f, indent=2, ensure_ascii=False)
        
        console.print(f"✅ [bold green]Profile created: {profile_path}")
        console.print("💡 Edit the file to customize RSS feeds, keywords, and other settings")
        
    except Exception as e:
        console.print(f"❌ [bold red]Error creating profile: {e}")


def main():
    """Main entry point for profile CLI"""
    parser = argparse.ArgumentParser(
        description="Profile Management CLI for YouTube Video Creator",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python profile_cli.py --list                    # List all profiles
  python profile_cli.py --show finance            # Show finance profile details
  python profile_cli.py --validate gaming_news    # Validate gaming_news profile
  python profile_cli.py --create                  # Create new profile interactively
        """
    )
    
    parser.add_argument(
        "--list",
        action="store_true",
        help="List all available profiles"
    )
    
    parser.add_argument(
        "--show",
        type=str,
        metavar="PROFILE",
        help="Show detailed information about a profile"
    )
    
    parser.add_argument(
        "--validate",
        type=str,
        metavar="PROFILE",
        help="Validate a specific profile"
    )
    
    parser.add_argument(
        "--create",
        action="store_true",
        help="Create a new profile interactively"
    )
    
    args = parser.parse_args()
    
    # Show header
    display_header()
    
    # Handle commands
    if args.list:
        list_profiles()
    elif args.show:
        show_profile(args.show)
    elif args.validate:
        validate_profile(args.validate)
    elif args.create:
        create_profile_interactive()
    else:
        # No command specified, show help
        console.print("🎯 [bold blue]Profile Management CLI")
        console.print("=" * 40)
        console.print()
        console.print("Available commands:")
        console.print("  --list             List all available profiles")
        console.print("  --show PROFILE     Show detailed profile information")
        console.print("  --validate PROFILE Validate a profile")
        console.print("  --create           Create a new profile interactively")
        console.print()
        console.print("Examples:")
        console.print("  python profile_cli.py --list")
        console.print("  python profile_cli.py --show finance")
        console.print("  python profile_cli.py --validate gaming_news")
        console.print("  python profile_cli.py --create")


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Profile Management CLI

Command-line interface for managing video creation profiles.
Provides tools for creating, editing, validating, and managing profiles.
"""

import argparse
import json
import sys
from pathlib import Path
from typing import Dict, Any

# Profile management imports
from Helpers.ProfileManager import <PERSON>Manager, ProfileError
from Helpers.ProfileValidator import ProfileValidator, ValidationError


def display_header():
    """Display a header for the profile CLI"""
    print()
    print("⚙️ Profile Manager CLI")
    print("Manage video creation profiles")
    print("=" * 40)
    print()


def list_profiles():
    """List all available profiles"""
    try:
        profile_manager = ProfileManager()
        profiles = profile_manager.list_profiles()

        if profiles:
            print("📋 Available Profiles:")
            print("-" * 60)
            print(f"{'Name':<15} {'Type':<12} {'Channel':<20} {'Status':<10}")
            print("-" * 60)

            for profile_name in profiles:
                try:
                    profile = profile_manager.load_profile(profile_name)
                    status = "✅ Valid"

                    # Skip validation for now (requires jsonschema)
                    status = "✅ Loaded"

                    # Clean up profile name for display
                    display_name = profile_name.replace('_profile', '')
                    print(f"{display_name:<15} {profile.profile_info.type:<12} {profile.profile_info.channel_name:<20} {status:<10}")

                except Exception as e:
                    display_name = profile_name.replace('_profile', '')
                    print(f"{display_name:<15} {'Error':<12} {'Unknown':<20} {'❌ Error':<10}")
                    print(f"    Error: {e}")

            print("-" * 60)
        else:
            print("⚠️  No profiles found")
            print("💡 Create profiles using --create or copy examples from profiles/examples/")

    except Exception as e:
        print(f"❌ Error listing profiles: {e}")


def validate_profile(profile_name: str):
    """Validate a specific profile"""
    try:
        profile_manager = ProfileManager()

        # Try with and without _profile suffix
        try:
            profile = profile_manager.load_profile(profile_name)
        except ProfileError:
            profile = profile_manager.load_profile(f"{profile_name}_profile")

        # Skip detailed validation for now (requires jsonschema)
        print(f"✅ Profile '{profile_name}' loaded successfully")
        print("💡 Install jsonschema for detailed validation: pip install jsonschema")

    except ProfileError as e:
        print(f"❌ Profile error: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")


def show_profile(profile_name: str):
    """Show detailed information about a profile"""
    try:
        profile_manager = ProfileManager()

        # Try with and without _profile suffix
        try:
            profile = profile_manager.load_profile(profile_name)
        except ProfileError:
            profile = profile_manager.load_profile(f"{profile_name}_profile")

        # Display profile info
        print(f"📋 Profile: {profile_name}")
        print("=" * 40)
        print(f"Name: {profile.profile_info.name}")
        print(f"Type: {profile.profile_info.type}")
        print(f"Channel Name: {profile.profile_info.channel_name}")
        print(f"Description: {profile.profile_info.description}")
        print(f"Target Audience: {profile.profile_info.target_audience}")
        print()

        # Display data sources
        if hasattr(profile.data_sources, 'rss_feeds') and profile.data_sources.rss_feeds:
            print("📰 RSS Feeds:")
            print("-" * 20)
            for feed in profile.data_sources.rss_feeds:
                print(f"  • {feed.name}: {feed.url}")
            print()

        # Display validation status
        print("✅ Profile loaded successfully")
        print("💡 Install jsonschema for detailed validation: pip install jsonschema")

    except ProfileError as e:
        print(f"❌ Profile error: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")


def create_profile_interactive():
    """Create a new profile interactively"""
    print("🎯 Creating New Profile")
    print("=" * 40)

    # Get basic information
    name = input("Profile name: ").strip()
    print("Content type options: finance, gaming_news, ai_news, tech_news, other")
    profile_type = input("Content type: ").strip()
    channel_name = input("YouTube channel name: ").strip()
    description = input("Profile description: ").strip()
    target_audience = input("Target audience: ").strip()
    
    # Create basic profile structure
    profile_data = {
        "profile_info": {
            "name": name,
            "type": profile_type,
            "channel_name": channel_name,
            "description": description,
            "target_audience": target_audience,
            "created_date": "2024-01-01",
            "version": "1.0"
        },
        "content_config": {
            "content_style": "educational",
            "content_tone": "professional",
            "video_length_seconds": 60,
            "scenes_per_video": 5,
            "creation_interval_hours": 2
        },
        "data_sources": {
            "rss_feeds": [],
            "google_trends": {
                "enabled": True,
                "region": "united_states",
                "category": "business_industrial",
                "count": 30
            },
            "search": {
                "max_results": 5,
                "time_limit_hours": 48
            }
        },
        "agents_config": {
            "story_picker": {
                "max_topics": 10,
                "avoid_duplicates": True,
                "rules": []
            },
            "search_terms": {
                "max_terms": 5,
                "include_synonyms": True,
                "focus_keywords": []
            },
            "data_extractor": {
                "extraction_criteria": []
            },
            "script_writer": {
                "style_guidelines": [],
                "template_phrases": []
            },
            "youtube_metadata": {
                "title_templates": [],
                "description_templates": [],
                "default_tags": []
            }
        },
        "upload_config": {
            "browser_profile": "default",
            "upload_schedule": "immediate",
            "privacy_setting": "public"
        },
        "media_config": {
            "tts": {
                "voice": "af_bella",
                "speed": 1.0,
                "language": "en"
            },
            "images": {
                "style": "photorealistic",
                "aspect_ratio": "9:16"
            },
            "video": {
                "resolution": "1080x1920",
                "fps": 30,
                "format": "mp4"
            }
        }
    }
    
    # Save profile
    try:
        profile_path = Path(f"profiles/examples/{name}_profile.json")
        profile_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(profile_path, 'w', encoding='utf-8') as f:
            json.dump(profile_data, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Profile created: {profile_path}")
        print("💡 Edit the file to customize RSS feeds, keywords, and other settings")

    except Exception as e:
        print(f"❌ Error creating profile: {e}")


def main():
    """Main entry point for profile CLI"""
    parser = argparse.ArgumentParser(
        description="Profile Management CLI for YouTube Video Creator",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python profile_cli.py --list                    # List all profiles
  python profile_cli.py --show finance            # Show finance profile details
  python profile_cli.py --validate gaming_news    # Validate gaming_news profile
  python profile_cli.py --create                  # Create new profile interactively
        """
    )
    
    parser.add_argument(
        "--list",
        action="store_true",
        help="List all available profiles"
    )
    
    parser.add_argument(
        "--show",
        type=str,
        metavar="PROFILE",
        help="Show detailed information about a profile"
    )
    
    parser.add_argument(
        "--validate",
        type=str,
        metavar="PROFILE",
        help="Validate a specific profile"
    )
    
    parser.add_argument(
        "--create",
        action="store_true",
        help="Create a new profile interactively"
    )
    
    args = parser.parse_args()
    
    # Show header
    display_header()
    
    # Handle commands
    if args.list:
        list_profiles()
    elif args.show:
        show_profile(args.show)
    elif args.validate:
        validate_profile(args.validate)
    elif args.create:
        create_profile_interactive()
    else:
        # No command specified, show help
        print("🎯 Profile Management CLI")
        print("=" * 40)
        print()
        print("Available commands:")
        print("  --list             List all available profiles")
        print("  --show PROFILE     Show detailed profile information")
        print("  --validate PROFILE Validate a profile")
        print("  --create           Create a new profile interactively")
        print()
        print("Examples:")
        print("  python profile_cli.py --list")
        print("  python profile_cli.py --show finance")
        print("  python profile_cli.py --validate gaming_news")
        print("  python profile_cli.py --create")


if __name__ == "__main__":
    main()

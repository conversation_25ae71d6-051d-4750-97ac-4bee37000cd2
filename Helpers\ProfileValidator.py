"""
Profile Validator for YouTube Video Creator

This module provides comprehensive validation for video creation profiles,
including schema validation, business logic validation, and configuration
consistency checks.
"""

import json
import logging
import re
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from urllib.parse import urlparse
import jsonschema
from jsonschema import validate, ValidationError


class ProfileValidationError(Exception):
    """Custom exception for profile validation errors"""
    pass


class ProfileValidator:
    """
    Comprehensive validator for video creation profiles
    
    Features:
    - JSON schema validation
    - Business logic validation
    - URL and configuration validation
    - Cross-field consistency checks
    - Detailed error reporting
    """
    
    def __init__(self, schema_path: Optional[str] = None):
        """
        Initialize the profile validator
        
        Args:
            schema_path: Path to JSON schema file for validation
        """
        self.schema_path = Path(schema_path) if schema_path else Path("profiles/schema/profile_schema.json")
        self.logger = logging.getLogger(__name__)
        self._schema_cache: Optional[Dict] = None
    
    def _load_schema(self) -> Dict:
        """Load and cache the JSON schema"""
        if self._schema_cache is None:
            if not self.schema_path.exists():
                raise ProfileValidationError(f"Schema file not found: {self.schema_path}")
            
            try:
                with open(self.schema_path, 'r', encoding='utf-8') as f:
                    self._schema_cache = json.load(f)
            except Exception as e:
                raise ProfileValidationError(f"Failed to load schema: {e}")
        
        return self._schema_cache
    
    def validate_schema(self, profile_data: Dict) -> List[str]:
        """
        Validate profile data against JSON schema
        
        Args:
            profile_data: Profile configuration dictionary
            
        Returns:
            List of validation errors (empty if valid)
        """
        errors = []
        
        try:
            schema = self._load_schema()
            validate(instance=profile_data, schema=schema)
        except ValidationError as e:
            errors.append(f"Schema validation error: {e.message}")
            if e.path:
                errors.append(f"Error location: {' -> '.join(str(p) for p in e.path)}")
        except Exception as e:
            errors.append(f"Schema validation failed: {e}")
        
        return errors
    
    def validate_urls(self, profile_data: Dict) -> List[str]:
        """
        Validate all URLs in the profile configuration
        
        Args:
            profile_data: Profile configuration dictionary
            
        Returns:
            List of URL validation errors
        """
        errors = []
        
        # Validate news source URLs
        if "data_sources" in profile_data and "news_sources" in profile_data["data_sources"]:
            for i, source in enumerate(profile_data["data_sources"]["news_sources"]):
                if "url" in source:
                    url = source["url"]
                    if not self._is_valid_url(url):
                        errors.append(f"Invalid URL in news source {i+1}: {url}")
        
        return errors
    
    def _is_valid_url(self, url: str) -> bool:
        """Check if a URL is valid"""
        try:
            result = urlparse(url)
            return all([result.scheme, result.netloc])
        except Exception:
            return False
    
    def validate_business_logic(self, profile_data: Dict) -> List[str]:
        """
        Validate business logic and configuration consistency
        
        Args:
            profile_data: Profile configuration dictionary
            
        Returns:
            List of business logic validation errors
        """
        errors = []
        
        # Validate profile name format
        if "profile_info" in profile_data and "name" in profile_data["profile_info"]:
            name = profile_data["profile_info"]["name"]
            if not re.match(r'^[a-z0-9_]+$', name):
                errors.append("Profile name must contain only lowercase letters, numbers, and underscores")
        
        # Validate version format
        if "profile_info" in profile_data and "version" in profile_data["profile_info"]:
            version = profile_data["profile_info"]["version"]
            if not re.match(r'^\d+\.\d+\.\d+$', version):
                errors.append("Version must follow semantic versioning format (e.g., 1.0.0)")
        
        # Validate content configuration
        if "content_config" in profile_data:
            content_config = profile_data["content_config"]
            
            # Check video length
            if "video_length_target" in content_config:
                length = content_config["video_length_target"]
                if not (15 <= length <= 60):
                    errors.append("Video length target must be between 15 and 60 seconds")
            
            # Check scenes per video
            if "scenes_per_video" in content_config:
                scenes = content_config["scenes_per_video"]
                if not (3 <= scenes <= 10):
                    errors.append("Scenes per video must be between 3 and 10")
            
            # Validate keywords
            if "keywords" in content_config:
                keywords = content_config["keywords"]
                if len(keywords) < 5:
                    errors.append("At least 5 keywords are recommended for effective content generation")
                if len(keywords) > 50:
                    errors.append("Too many keywords (max 50 recommended)")
            
            # Validate hashtags
            if "hashtags" in content_config:
                hashtags = content_config["hashtags"]
                for hashtag in hashtags:
                    if not hashtag.startswith('#'):
                        errors.append(f"Hashtag must start with #: {hashtag}")
                    if len(hashtag) > 100:
                        errors.append(f"Hashtag too long (max 100 chars): {hashtag}")
        
        # Validate agent configurations
        if "agents_config" in profile_data:
            agents = profile_data["agents_config"]
            
            # Check system prompts
            for agent_name, agent_config in agents.items():
                if "system_prompt" in agent_config:
                    prompt = agent_config["system_prompt"]
                    if len(prompt) < 50:
                        errors.append(f"{agent_name} system prompt is too short (min 50 chars)")
                    if len(prompt) > 2000:
                        errors.append(f"{agent_name} system prompt is too long (max 2000 chars)")
            
            # Validate script writer temperature
            if "script_writer" in agents and "temperature" in agents["script_writer"]:
                temp = agents["script_writer"]["temperature"]
                if not (0.0 <= temp <= 2.0):
                    errors.append("Script writer temperature must be between 0.0 and 2.0")
        
        # Validate upload configuration
        if "upload_config" in profile_data:
            upload_config = profile_data["upload_config"]
            
            # Validate browser profile name
            if "browser_profile" in upload_config and "name" in upload_config["browser_profile"]:
                browser_name = upload_config["browser_profile"]["name"]
                if not re.match(r'^[a-z0-9_]+$', browser_name):
                    errors.append("Browser profile name must contain only lowercase letters, numbers, and underscores")
            
            # Validate YouTube settings
            if "youtube_settings" in upload_config:
                yt_settings = upload_config["youtube_settings"]
                
                # Check default tags
                if "default_tags" in yt_settings:
                    tags = yt_settings["default_tags"]
                    if len(tags) > 15:
                        errors.append("Too many default tags (max 15 recommended)")
                    
                    total_tag_length = sum(len(tag) for tag in tags)
                    if total_tag_length > 400:
                        errors.append("Total tag length too long (max 400 chars)")
        
        return errors
    
    def validate_data_sources(self, profile_data: Dict) -> List[str]:
        """
        Validate data source configurations
        
        Args:
            profile_data: Profile configuration dictionary
            
        Returns:
            List of data source validation errors
        """
        errors = []
        
        if "data_sources" not in profile_data:
            return errors
        
        data_sources = profile_data["data_sources"]
        
        # Validate news sources
        if "news_sources" in data_sources:
            news_sources = data_sources["news_sources"]
            
            if len(news_sources) == 0:
                errors.append("At least one news source is required")
            
            source_names = []
            for i, source in enumerate(news_sources):
                # Check for duplicate names
                if "name" in source:
                    if source["name"] in source_names:
                        errors.append(f"Duplicate news source name: {source['name']}")
                    source_names.append(source["name"])
                
                # Validate source type
                if "type" in source and source["type"] not in ["rss", "api", "scraper"]:
                    errors.append(f"Invalid news source type in source {i+1}: {source['type']}")
        
        # Validate trends configuration
        if "trends_config" in data_sources:
            trends = data_sources["trends_config"]
            
            # Check keywords
            if "keywords" in trends:
                keywords = trends["keywords"]
                if len(keywords) > 5:
                    errors.append("Too many trend keywords (max 5 for API limits)")
        
        return errors
    
    def validate_complete_profile(self, profile_data: Dict) -> Tuple[bool, List[str]]:
        """
        Perform complete validation of a profile
        
        Args:
            profile_data: Profile configuration dictionary
            
        Returns:
            Tuple of (is_valid, list_of_errors)
        """
        all_errors = []
        
        # Schema validation
        all_errors.extend(self.validate_schema(profile_data))
        
        # URL validation
        all_errors.extend(self.validate_urls(profile_data))
        
        # Business logic validation
        all_errors.extend(self.validate_business_logic(profile_data))
        
        # Data source validation
        all_errors.extend(self.validate_data_sources(profile_data))
        
        is_valid = len(all_errors) == 0
        
        if is_valid:
            self.logger.info("Profile validation passed")
        else:
            self.logger.warning(f"Profile validation failed with {len(all_errors)} errors")
        
        return is_valid, all_errors
    
    def validate_profile_file(self, profile_path: str) -> Tuple[bool, List[str]]:
        """
        Validate a profile file
        
        Args:
            profile_path: Path to the profile JSON file
            
        Returns:
            Tuple of (is_valid, list_of_errors)
        """
        try:
            with open(profile_path, 'r', encoding='utf-8') as f:
                profile_data = json.load(f)
            
            return self.validate_complete_profile(profile_data)
            
        except Exception as e:
            return False, [f"Failed to load profile file: {e}"]


def create_profile_validator(schema_path: Optional[str] = None) -> ProfileValidator:
    """
    Convenience function to create a profile validator
    
    Args:
        schema_path: Path to JSON schema file for validation
        
    Returns:
        Configured ProfileValidator instance
    """
    return ProfileValidator(schema_path=schema_path)

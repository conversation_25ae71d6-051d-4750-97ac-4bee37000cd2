#!/usr/bin/env python3
"""
Fix ChromeDriver version mismatch by clearing cache and forcing download
"""

import sys
import os
import shutil
from pathlib import Path

def fix_chromedriver():
    """Fix ChromeDriver version mismatch"""
    try:
        print("🔧 Fixing ChromeDriver version mismatch...")

        # Clear webdriver-manager cache
        print("🧹 Clearing webdriver-manager cache...")
        try:
            from webdriver_manager.chrome import ChromeDriverManager

            # Get cache directory and clear it
            manager = ChromeDriverManager()

            # Try to get the cache directory
            cache_paths = [
                os.path.expanduser("~/.wdm"),
                os.path.expanduser("~/AppData/Local/.wdm"),
                os.path.expanduser("~/AppData/Roaming/.wdm"),
                ".wdm"
            ]

            for cache_path in cache_paths:
                if os.path.exists(cache_path):
                    print(f"📁 Clearing cache at: {cache_path}")
                    shutil.rmtree(cache_path, ignore_errors=True)

            print("✅ Cache cleared")

            # Force download of ChromeDriver for version 137
            print("📥 Downloading ChromeDriver for Chrome 137...")
            manager = ChromeDriverManager(driver_version="137.0.7151.119")
            driver_path = manager.install()
            print(f"✅ ChromeDriver installed at: {driver_path}")

        except Exception as e:
            print(f"⚠️ Cache clearing failed: {e}")
            # Try without specific version
            try:
                print("🔄 Trying without specific version...")
                manager = ChromeDriverManager()
                driver_path = manager.install()
                print(f"✅ ChromeDriver installed at: {driver_path}")
            except Exception as e2:
                print(f"❌ Failed to install ChromeDriver: {e2}")
                return False

        # Test the ChromeDriver
        print("🧪 Testing ChromeDriver...")
        try:
            import undetected_chromedriver as uc

            options = uc.ChromeOptions()
            options.add_argument("--headless")
            options.add_argument("--no-sandbox")
            options.add_argument("--disable-dev-shm-usage")
            options.add_argument("--disable-gpu")

            # Try to create a driver instance
            print("🔄 Creating Chrome driver...")
            driver = uc.Chrome(options=options, version_main=None)
            print("🌐 Testing navigation...")
            driver.get("https://www.google.com")
            print(f"✅ ChromeDriver test successful! Version: {driver.capabilities.get('browserVersion', 'Unknown')}")
            driver.quit()

        except Exception as e:
            print(f"❌ ChromeDriver test failed: {e}")
            print(f"Error details: {str(e)}")
            return False

        print("🎉 ChromeDriver fix completed successfully!")
        return True

    except Exception as e:
        print(f"❌ Fix failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = fix_chromedriver()
    sys.exit(0 if success else 1)

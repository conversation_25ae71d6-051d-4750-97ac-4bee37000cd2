#!/usr/bin/env python3
"""
Simple test script to verify profile system works
"""

import json
from pathlib import Path

def test_profile_loading():
    """Test basic profile loading without dependencies"""
    profiles_dir = Path("profiles/examples")
    
    if not profiles_dir.exists():
        print("❌ Profiles directory not found")
        return False
    
    profile_files = list(profiles_dir.glob("*_profile.json"))
    
    if not profile_files:
        print("❌ No profile files found")
        return False
    
    print(f"✅ Found {len(profile_files)} profile files:")
    
    for profile_file in profile_files:
        try:
            with open(profile_file, 'r', encoding='utf-8') as f:
                profile_data = json.load(f)
            
            profile_name = profile_file.stem.replace('_profile', '')
            profile_info = profile_data.get('profile_info', {})
            
            print(f"  📋 {profile_name}")
            print(f"     Type: {profile_info.get('type', 'Unknown')}")
            print(f"     Channel: {profile_info.get('channel_name', 'Unknown')}")
            print(f"     Description: {profile_info.get('description', 'No description')}")
            print()
            
        except Exception as e:
            print(f"  ❌ Error loading {profile_file.name}: {e}")
    
    return True

def test_agent_imports():
    """Test if agent imports work"""
    try:
        from Agents.BaseAgent import BaseAgent
        print("✅ BaseAgent import successful")
        
        from Agents.StoryPicker import StoryPickerAgent
        print("✅ StoryPickerAgent import successful")
        
        from Agents.SearchTerms import SearchTermsAgent
        print("✅ SearchTermsAgent import successful")
        
        from Agents.DataExtractor import DataExtractorAgent
        print("✅ DataExtractorAgent import successful")
        
        from Agents.ScriptWriter import ScriptWriterAgent
        print("✅ ScriptWriterAgent import successful")
        
        from Agents.YouTubeMetadata import YouTubeMetadataAgent
        print("✅ YouTubeMetadataAgent import successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Agent import error: {e}")
        return False

def test_agent_factory():
    """Test agent factory import"""
    try:
        from Agents.AgentFactory import AgentFactory
        print("✅ AgentFactory import successful")
        
        factory = AgentFactory()
        available_types = factory.get_available_agent_types()
        print(f"✅ Available agent types: {available_types}")
        
        return True
        
    except Exception as e:
        print(f"❌ AgentFactory error: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Testing Profile System")
    print("=" * 40)
    print()
    
    # Test 1: Profile loading
    print("Test 1: Profile Loading")
    print("-" * 25)
    success1 = test_profile_loading()
    print()
    
    # Test 2: Agent imports
    print("Test 2: Agent Imports")
    print("-" * 20)
    success2 = test_agent_imports()
    print()
    
    # Test 3: Agent factory
    print("Test 3: Agent Factory")
    print("-" * 20)
    success3 = test_agent_factory()
    print()
    
    # Summary
    print("Summary")
    print("-" * 10)
    if success1 and success2 and success3:
        print("✅ All tests passed! Profile system is ready.")
    else:
        print("❌ Some tests failed. Check the errors above.")
        if not success1:
            print("  - Profile loading failed")
        if not success2:
            print("  - Agent imports failed")
        if not success3:
            print("  - Agent factory failed")

if __name__ == "__main__":
    main()

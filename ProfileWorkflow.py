"""
Profile-aware video creation workflow

This module provides a refactored video creation workflow that uses
profile-based configuration instead of hardcoded logic.
"""

import time
import os
import shutil
import json
import logging
import random
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Any

# Rich imports for beautiful console output
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TaskProgressColumn, TimeElapsedColumn
from rich.panel import Panel
from rich.text import Text
from rich.table import Table
from rich import box
from rich.align import Align
from rich.status import Status

# Profile and agent imports
from Helpers.ProfileManager import ProfileManager, VideoProfile
from Agents.AgentFactory import create_all_agents, AgentFactoryError

# Data source imports
from Helpers.GoogleTrends import GoogleTrendsClient
from Helpers.DuckDuckGoSearch import DuckDuckGoSearchClient, SafeSearch
from Helpers.WebScraper import WebScraperClient

# Media generation imports
from Helpers.GeminiImageGen import GeminiImageGen, GeminiImageGenError
from Helpers.KokoroTTS import create_tts_client, KokoroTTSError
from Helpers.VideoAssembler import VideoAssembler, VideoAssemblerError, VideoConfig
from Helpers.SubtitleOverlay import SubtitleOverlay, SubtitleOverlayError, SubtitleTiming

# Upload and tracking imports
from Helpers.YouTubeUploader import YouTubeUploader, UploadConfig, YouTubeUploaderError
from Helpers.VideoTracker import VideoTracker, VideoRecord

# Retry mechanism imports
from Helpers.RetryMechanism import create_image_retry_mechanism, create_llm_retry_mechanism, RetryError

# Initialize rich console
console = Console()


class ProfileWorkflowError(Exception):
    """Custom exception for profile workflow errors"""
    pass


class ProfileWorkflow:
    """
    Profile-aware video creation workflow
    
    Features:
    - Profile-based configuration and agent creation
    - Configurable data sources and content generation
    - Support for different video types and styles
    - Rich console output with progress tracking
    - Error handling and retry mechanisms
    """
    
    def __init__(self, profile_name: str, video_tracker: VideoTracker, quiet_mode: bool = False):
        """
        Initialize the profile workflow
        
        Args:
            profile_name: Name of the profile to use
            video_tracker: Video tracker instance
            quiet_mode: Whether to suppress console output
        """
        self.profile_name = profile_name
        self.video_tracker = video_tracker
        self.quiet_mode = quiet_mode
        self.logger = logging.getLogger(__name__)
        
        # Load profile
        self.profile_manager = ProfileManager()
        self.profile = self.profile_manager.load_profile(profile_name)
        
        # Create agents
        try:
            self.agents = create_all_agents(self.profile)
        except AgentFactoryError as e:
            raise ProfileWorkflowError(f"Failed to create agents: {e}")
        
        # Session statistics
        self.session_stats = {
            "Profile": profile_name,
            "Topics Processed": 0,
            "Videos Created": 0,
            "Videos Uploaded": 0,
            "Total Runtime": "0:00:00",
            "Success Rate": "0%"
        }
        
        self.start_time = time.time()
    
    def print_if_not_quiet(self, *args, **kwargs):
        """Print to console if not in quiet mode"""
        if not self.quiet_mode:
            console.print(*args, **kwargs)
    
    def update_runtime_stats(self):
        """Update runtime statistics"""
        elapsed = time.time() - self.start_time
        hours, remainder = divmod(elapsed, 3600)
        minutes, seconds = divmod(remainder, 60)
        self.session_stats["Total Runtime"] = f"{int(hours):02d}:{int(minutes):02d}:{int(seconds):02d}"
        
        # Calculate success rate
        if self.session_stats["Topics Processed"] > 0:
            success_rate = (self.session_stats["Videos Created"] / self.session_stats["Topics Processed"]) * 100
            self.session_stats["Success Rate"] = f"{success_rate:.1f}%"
    
    def create_stats_table(self):
        """Create a beautiful stats table"""
        table = Table(title="📊 Session Statistics", box=box.ROUNDED, border_style="cyan")
        
        table.add_column("Metric", style="bold white", no_wrap=True)
        table.add_column("Value", style="bold green", justify="right")
        
        for key, value in self.session_stats.items():
            table.add_row(key, str(value))
        
        return table
    
    def display_topic_processing(self, topic: str):
        """Display topic processing header"""
        topic_panel = Panel(
            Align.center(
                Text(f"🎯 Processing Topic: {topic}", style="bold yellow")
            ),
            box=box.HEAVY,
            border_style="yellow",
            padding=(0, 2)
        )
        console.print(topic_panel)
    
    def gather_trending_topics(self) -> List[str]:
        """Gather trending topics based on profile configuration"""
        trending_topics = []
        
        # Get Google Trends if configured
        if hasattr(self.profile.data_sources, 'trends_config') and self.profile.data_sources.trends_config:
            trends_config = self.profile.data_sources.trends_config

            if not self.quiet_mode:
                with console.status("[bold green]🔍 Gathering trending searches...", spinner="dots"):
                    client = GoogleTrendsClient()
                    trends = client.get_trending_searches(
                        pn=trends_config.region,
                        count=30,  # Default count since it's not in profile config
                        category=trends_config.category
                    )
            else:
                client = GoogleTrendsClient()
                trends = client.get_trending_searches(
                    pn=trends_config.region,
                    count=30,  # Default count since it's not in profile config
                    category=trends_config.category
                )

            trending_topics.extend(trends)
            self.print_if_not_quiet(f"✅ [bold green]Gathered {len(trends)} trending searches")
        
        return trending_topics
    
    def gather_news_titles(self) -> List[str]:
        """Gather news titles from RSS feeds"""
        news_titles = []
        
        # Get RSS feeds if configured
        if hasattr(self.profile.data_sources, 'rss_feeds') and self.profile.data_sources.rss_feeds:
            # TODO: Implement RSS feed scraping based on profile configuration
            # This would replace the hardcoded Yahoo Finance scraping
            pass
        
        return news_titles
    
    def select_topic(self, trending_topics: List[str], news_titles: List[str]) -> Optional[str]:
        """Select a topic using the story picker agent"""
        # Get today's topics to avoid duplicates
        todays_used_topics = self.video_tracker.get_todays_topics()
        
        if not self.quiet_mode:
            with console.status("[bold blue]🤖 Analyzing trends and picking story topics...", spinner="dots"):
                story_topics = self.agents['story_picker'].execute(todays_used_topics, trending_topics, news_titles)
        else:
            story_topics = self.agents['story_picker'].execute(todays_used_topics, trending_topics, news_titles)
        
        if len(story_topics) == 0:
            self.print_if_not_quiet("⚠️  [bold yellow]No new story topics found")
            return None
        
        self.print_if_not_quiet(f"✅ [bold green]Found {len(story_topics)} potential topics")
        
        # Select one random topic that's not a duplicate
        random.shuffle(story_topics)
        
        for topic in story_topics:
            # Check for duplicate topics
            if not self.video_tracker.is_duplicate_topic(topic, similarity_threshold=0.8):
                return topic
            else:
                self.print_if_not_quiet(f"⚠️  [bold yellow]Skipping duplicate topic: {topic}")
        
        self.print_if_not_quiet("⚠️  [bold yellow]All topics are duplicates")
        return None
    
    def generate_search_terms(self, topic: str) -> List[str]:
        """Generate search terms for the topic"""
        return self.agents['search_terms'].execute(topic)
    
    def gather_content(self, topic: str, search_terms: List[str]) -> List[str]:
        """Gather content from search results"""
        gathered_results = []
        
        # Configure search client based on profile
        search_config = getattr(self.profile.data_sources, 'search', None)
        max_results = search_config.max_results if search_config else 5
        
        client = DuckDuckGoSearchClient(
            max_results=max_results,
            safe_search=SafeSearch.OFF,
            enable_cache=True,
            time_limit_hours=48
        )
        
        with WebScraperClient(headless=True, enable_cache=True) as scraper:
            for term in search_terms:
                response = client.search(term)
                self.print_if_not_quiet(f"  📊 Found {response.total_results} results in {response.search_time:.2f}s for '{term}'")
                
                for i, result in enumerate(response.results[:2], 1):
                    self.print_if_not_quiet(f"    🔍 Processing result {i}: {result.href}")
                    scraped_result = scraper.extract_text(result.href)
                    
                    if scraped_result.success and len(scraped_result.text_content) > 0:
                        self.print_if_not_quiet(f"    📄 Scraped {len(scraped_result.text_content)} characters from {result.href}")
                        # Use scraped content directly since data_extractor agent was removed
                        if len(scraped_result.text_content.strip()) > 100:  # Basic content quality check
                            self.print_if_not_quiet(f"    ✅ Using {len(scraped_result.text_content)} characters of content")
                            gathered_results.append(scraped_result.text_content)
                        else:
                            self.print_if_not_quiet(f"    ⚠️  Content too short, skipping")
                    else:
                        error_msg = scraped_result.error_message if hasattr(scraped_result, 'error_message') else "Unknown error"
                        self.print_if_not_quiet(f"    ❌ Failed to scrape {result.href}: {error_msg}")
        
        return gathered_results
    
    def run_single_iteration(self, enable_upload: bool = True) -> bool:
        """
        Run a single iteration of the video creation workflow
        
        Args:
            enable_upload: Whether to upload the created video
            
        Returns:
            True if video was successfully created, False otherwise
        """
        try:
            # Update runtime stats
            self.update_runtime_stats()
            
            # Step 1: Gather trending topics
            trending_topics = self.gather_trending_topics()
            
            # Step 2: Gather news titles
            news_titles = self.gather_news_titles()
            
            # Step 3: Select topic
            topic = self.select_topic(trending_topics, news_titles)
            if topic is None:
                return False
            
            # Update stats
            self.session_stats["Topics Processed"] += 1
            
            self.print_if_not_quiet(f"🎯 [bold blue]Selected topic: {topic}")
            if not self.quiet_mode:
                self.display_topic_processing(topic)
            
            # Step 4: Generate search terms
            search_terms = self.generate_search_terms(topic)
            self.print_if_not_quiet(f"✅ Generated {len(search_terms)} search terms")
            
            # Step 5: Gather content
            gathered_results = self.gather_content(topic, search_terms)
            self.print_if_not_quiet(f"✅ Gathered {len(gathered_results)} content pieces")
            
            if not gathered_results:
                self.print_if_not_quiet("⚠️  [bold yellow]No content gathered, skipping video creation")
                return False
            
            # Step 6: Generate script
            combined_content = " ".join(gathered_results)
            script_prompt = f"Topic: {topic}\n\nResearch Content:\n{combined_content}"
            script = self.agents['script_writer'].execute(script_prompt)
            self.print_if_not_quiet(f"✅ Generated script with {len(script.scenes)} scenes")
            
            # Step 7: Generate metadata
            metadata = self.agents['youtube_metadata'].execute(script, topic)
            self.print_if_not_quiet(f"✅ Generated metadata: {metadata.title}")

            # Step 8: Create temp directory and generate media files
            temp_dir = Path("temp")
            if temp_dir.exists():
                shutil.rmtree(temp_dir)
                self.print_if_not_quiet("  🧹 Cleared existing temp directory")
            temp_dir.mkdir(parents=True, exist_ok=True)

            # Step 9: Generate images for each scene
            self.print_if_not_quiet("🎨 Generating scene images...")
            try:
                from Helpers.GeminiImageGen import GeminiImageGen
                image_gen = GeminiImageGen(output_dir=str(temp_dir), config=None)

                generated_images = []
                for i, scene in enumerate(script.scenes):
                    try:
                        image_result = image_gen.generate_image(
                            prompt=scene.image_prompt,
                            output_filename=f"SCENE_IMAGE_{i+1:02d}"
                        )
                        image_path = image_result.file_path
                        generated_images.append(image_path)
                        self.print_if_not_quiet(f"  ✅ Generated image {i+1}/{len(script.scenes)}")
                    except Exception as e:
                        self.print_if_not_quiet(f"  ❌ Failed to generate image {i+1}: {e}")
                        return False

            except Exception as e:
                self.print_if_not_quiet(f"❌ Image generation failed: {e}")
                return False

            # Step 10: Generate audio for each scene
            self.print_if_not_quiet("🎵 Generating scene audio...")
            try:
                from Helpers.KokoroTTS import KokoroTTS
                tts = KokoroTTS(output_dir=str(temp_dir))

                generated_audio = []
                for i, scene in enumerate(script.scenes):
                    try:
                        audio_result = tts.generate_audio(
                            text=scene.text,
                            output_filename=f"SCENE_AUDIO_{i+1:02d}"
                        )
                        audio_path = audio_result.file_path
                        generated_audio.append(audio_path)
                        self.print_if_not_quiet(f"  ✅ Generated audio {i+1}/{len(script.scenes)}")
                    except Exception as e:
                        self.print_if_not_quiet(f"  ❌ Failed to generate audio {i+1}: {e}")
                        return False

            except Exception as e:
                self.print_if_not_quiet(f"❌ Audio generation failed: {e}")
                return False

            # Step 11: Assemble video from scenes
            if generated_images and generated_audio and len(generated_images) == len(generated_audio):
                self.print_if_not_quiet("🎬 Assembling video from scenes...")
                try:
                    from Helpers.VideoAssembler import VideoAssembler, VideoConfig

                    video_config = VideoConfig(width=1080, height=1920, fps=30)
                    video_assembler = VideoAssembler(config=video_config, output_dir=str(temp_dir))

                    video_metadata = video_assembler.assemble_from_directories(
                        images_dir=str(temp_dir),
                        audio_dir=str(temp_dir),
                        output_filename="assembled_video",
                        image_pattern="SCENE_IMAGE_*.png",
                        audio_pattern="SCENE_AUDIO_*.wav"
                    )

                    self.print_if_not_quiet(f"✅ Video assembled: {video_metadata.file_path}")

                    # Save metadata to file
                    metadata_file = temp_dir / "youtube_metadata.json"
                    with open(metadata_file, 'w', encoding='utf-8') as f:
                        json.dump({
                            'title': metadata.title,
                            'description': metadata.description,
                            'tags': metadata.tags
                        }, f, indent=2)

                    self.print_if_not_quiet(f"✅ Saved metadata to: {metadata_file}")

                except Exception as e:
                    self.print_if_not_quiet(f"❌ Video assembly failed: {e}")
                    return False
            else:
                self.print_if_not_quiet("❌ Mismatch in generated media files")
                return False

            self.session_stats["Videos Created"] += 1
            return True
            
        except Exception as e:
            self.logger.error(f"Error in workflow iteration: {e}")
            self.print_if_not_quiet(f"❌ [bold red]Workflow error: {e}")
            return False
    
    def run_continuous(self, enable_upload: bool = True):
        """
        Run the workflow continuously
        
        Args:
            enable_upload: Whether to upload created videos
        """
        self.print_if_not_quiet(f"🚀 [bold green]Starting {self.profile_name} Video Creation Workflow")
        self.print_if_not_quiet("=" * 60)
        
        if not enable_upload:
            self.print_if_not_quiet("⚠️  [bold yellow]Upload disabled")
        
        while True:
            try:
                success = self.run_single_iteration(enable_upload)
                
                if not success:
                    self.print_if_not_quiet("⚠️  [bold yellow]No video created, waiting 1 hour...")
                    if not self.quiet_mode:
                        console.print(self.create_stats_table())
                    time.sleep(3600)
                    continue
                
                # Show stats after successful creation
                if not self.quiet_mode:
                    console.print(self.create_stats_table())
                
                # Wait before next iteration (configurable based on profile)
                wait_time = getattr(self.profile.content_config, 'creation_interval_hours', 2) * 3600
                self.print_if_not_quiet(f"✅ [bold green]Video created successfully, waiting {wait_time/3600:.1f} hours...")
                time.sleep(wait_time)
                
            except KeyboardInterrupt:
                self.print_if_not_quiet("\n🛑 [bold red]Workflow stopped by user")
                break
            except Exception as e:
                self.logger.error(f"Unexpected error in workflow: {e}")
                self.print_if_not_quiet(f"❌ [bold red]Unexpected error: {e}")
                self.print_if_not_quiet("⏳ Waiting 30 minutes before retry...")
                time.sleep(1800)  # Wait 30 minutes before retry


def run_profile_workflow(
    profile_name: str,
    video_tracker: VideoTracker,
    enable_upload: bool = True,
    quiet_mode: bool = False
):
    """
    Run the profile-aware video creation workflow
    
    Args:
        profile_name: Name of the profile to use
        video_tracker: Video tracker instance
        enable_upload: Whether to upload created videos
        quiet_mode: Whether to suppress console output
    """
    try:
        workflow = ProfileWorkflow(profile_name, video_tracker, quiet_mode)
        workflow.run_continuous(enable_upload)
    except ProfileWorkflowError as e:
        console.print(f"❌ [bold red]Profile workflow error: {e}")
    except Exception as e:
        console.print(f"❌ [bold red]Unexpected error: {e}")
